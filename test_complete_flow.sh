#!/bin/bash

# 完整流程测试脚本
set -e

BASE_URL="http://localhost:8080"
API_BASE="$BASE_URL/api/v1"
PHONE="15688515913"

echo "=== Solve Go API 完整流程测试 ==="
echo "测试手机号: $PHONE"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 1. 健康检查
echo -e "${YELLOW}1. 健康检查${NC}"
health_response=$(curl -s "$BASE_URL/health")
echo "响应: $health_response"
echo

# 2. 发送短信验证码
echo -e "${YELLOW}2. 发送短信验证码${NC}"
sms_response=$(curl -s -X POST "$API_BASE/send-sms" \
  -H "Content-Type: application/json" \
  -d "{\"phone\": \"$PHONE\"}")

echo "响应: $sms_response"

# 提取验证码（开发环境会返回验证码）
verification_code=$(echo "$sms_response" | grep -o '"code":"[^"]*"' | cut -d'"' -f4)

if [ -n "$verification_code" ]; then
    echo -e "${GREEN}✓ 短信发送成功，验证码: $verification_code${NC}"
else
    echo -e "${RED}✗ 短信发送失败或未返回验证码${NC}"
    echo "请手动输入收到的验证码:"
    read verification_code
fi
echo

# 3. 用户注册
echo -e "${YELLOW}3. 用户注册${NC}"
register_response=$(curl -s -X POST "$API_BASE/register" \
  -H "Content-Type: application/json" \
  -d "{
    \"phone\": \"$PHONE\",
    \"password\": \"123456\",
    \"nickname\": \"测试用户\",
    \"sms_code\": \"$verification_code\"
  }")

echo "响应: $register_response"

# 检查注册是否成功
if echo "$register_response" | grep -q '"code":200'; then
    echo -e "${GREEN}✓ 用户注册成功${NC}"
else
    echo -e "${RED}✗ 用户注册失败${NC}"
fi
echo

# 4. 管理员登录并激活用户
echo -e "${YELLOW}4. 管理员登录并激活用户${NC}"
admin_login_response=$(curl -s -X POST "$API_BASE/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800000001",
    "password": "123456"
  }')

admin_token=$(echo "$admin_login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$admin_token" ]; then
    echo -e "${GREEN}✓ 管理员登录成功${NC}"
    
    # 激活用户（假设新用户ID为2）
    activate_response=$(curl -s -X POST "$API_BASE/admin/users/2/activate" \
      -H "Authorization: Bearer $admin_token")
    
    echo "激活响应: $activate_response"
    echo -e "${GREEN}✓ 用户激活完成${NC}"
else
    echo -e "${RED}✗ 管理员登录失败${NC}"
    exit 1
fi
echo

# 5. 用户登录
echo -e "${YELLOW}5. 用户登录${NC}"
user_login_response=$(curl -s -X POST "$API_BASE/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"phone\": \"$PHONE\",
    \"password\": \"123456\"
  }")

echo "响应: $user_login_response"

user_token=$(echo "$user_login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$user_token" ]; then
    echo -e "${GREEN}✓ 用户登录成功${NC}"
    echo "Token: ${user_token:0:20}..."
else
    echo -e "${RED}✗ 用户登录失败${NC}"
    exit 1
fi
echo

# 6. 获取用户信息
echo -e "${YELLOW}6. 获取用户信息${NC}"
profile_response=$(curl -s -X GET "$API_BASE/user/profile" \
  -H "Authorization: Bearer $user_token")

echo "响应: $profile_response"
echo

# 7. 创建应用
echo -e "${YELLOW}7. 创建应用${NC}"
app_response=$(curl -s -X POST "$API_BASE/apps" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $user_token" \
  -d '{
    "name": "测试应用"
  }')

echo "响应: $app_response"

app_id=$(echo "$app_response" | grep -o '"app_id":"[^"]*"' | cut -d'"' -f4)
app_secret=$(echo "$app_response" | grep -o '"app_secret":"[^"]*"' | cut -d'"' -f4)

if [ -n "$app_id" ] && [ -n "$app_secret" ]; then
    echo -e "${GREEN}✓ 应用创建成功${NC}"
    echo "App ID: $app_id"
    echo "App Secret: ${app_secret:0:10}..."
else
    echo -e "${RED}✗ 应用创建失败${NC}"
    exit 1
fi
echo

# 8. 获取应用列表
echo -e "${YELLOW}8. 获取应用列表${NC}"
apps_response=$(curl -s -X GET "$API_BASE/apps" \
  -H "Authorization: Bearer $user_token")

echo "响应: $apps_response"
echo

echo -e "${GREEN}=== 测试完成 ===${NC}"
echo "总结："
echo "✓ 健康检查通过"
echo "✓ 短信验证码发送成功"
echo "✓ 用户注册成功"
echo "✓ 管理员激活用户成功"
echo "✓ 用户登录成功"
echo "✓ 获取用户信息成功"
echo "✓ 创建应用成功"
echo "✓ 获取应用列表成功"
echo
echo "应用信息："
echo "App ID: $app_id"
echo "App Secret: $app_secret"
echo
echo "注意：解题API需要配置有效的模型API密钥才能正常工作"
