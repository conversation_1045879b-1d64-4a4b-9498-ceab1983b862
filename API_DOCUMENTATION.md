# Solve Go API 接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token

## 响应格式

所有接口统一返回格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 1. 用户认证接口

### 1.1 发送短信验证码

**接口地址**: `POST /send-sms`

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": {
    "code": "123456"
  }
}
```

### 1.2 用户注册

**接口地址**: `POST /register`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "123456",
  "nickname": "用户昵称",
  "sms_code": "123456"
}
```

**参数说明**:
- `phone`: 手机号（必填）
- `password`: 密码，最少6位（必填）
- `nickname`: 用户昵称（必填）
- `sms_code`: 短信验证码（必填）

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功，请等待管理员激活账户",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "nickname": "用户昵称"
  }
}
```

### 1.3 用户登录

**接口地址**: `POST /login`

**请求参数**:
```json
{
  "phone": "13800138000",
  "password": "123456"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "phone": "13800138000",
      "nickname": "用户昵称",
      "role": "user",
      "balance": 1000
    }
  }
}
```

### 1.4 获取用户信息

**接口地址**: `GET /user/profile`

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "nickname": "用户昵称",
    "role": "user",
    "balance": 1000,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.5 更新用户信息

**接口地址**: `PUT /user/profile`

**请求头**:
```
Authorization: Bearer <token>
```

**请求参数**:
```json
{
  "nickname": "新昵称"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功"
}
```

### 1.6 修改密码

**接口地址**: `POST /user/change-password`

**请求头**:
```
Authorization: Bearer <token>
```

**请求参数**:
```json
{
  "old_password": "123456",
  "new_password": "654321"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码修改成功"
}
```

### 1.7 获取积分变动记录

**接口地址**: `GET /user/balance-logs`

**请求头**:
```
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码（可选，默认1）
- `page_size`: 每页数量（可选，默认10）

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "logs": [
      {
        "id": 1,
        "change_amount": -10,
        "reason": "解题消费",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

---

## 2. 应用管理接口

### 2.1 获取应用列表

**接口地址**: `GET /apps/`

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "user_id": 1,
      "name": "我的应用",
      "app_id": "app_1234567890ab",
      "app_secret": "secret_abcdef1234567890abcdef12",
      "status": 0,
      "total_calls": 100,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2.2 创建应用

**接口地址**: `POST /apps/`

**请求头**:
```
Authorization: Bearer <token>
```

**请求参数**:
```json
{
  "name": "我的应用"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "应用创建成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的应用",
    "app_id": "app_1234567890ab",
    "app_secret": "secret_abcdef1234567890abcdef12",
    "status": 0,
    "total_calls": 0,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2.3 更新应用

**接口地址**: `PUT /apps/:id`

**请求头**:
```
Authorization: Bearer <token>
```

**路径参数**:
- `id`: 应用ID

**请求参数**:
```json
{
  "name": "新应用名称"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "应用更新成功"
}
```

### 2.4 删除应用

**接口地址**: `DELETE /apps/:id`

**请求头**:
```
Authorization: Bearer <token>
```

**路径参数**:
- `id`: 应用ID

**响应示例**:
```json
{
  "code": 200,
  "message": "应用删除成功"
}
```

### 2.5 重置应用密钥

**接口地址**: `POST /apps/:id/reset-secret`

**请求头**:
```
Authorization: Bearer <token>
```

**路径参数**:
- `id`: 应用ID

**响应示例**:
```json
{
  "code": 200,
  "message": "密钥重置成功",
  "data": {
    "app_secret": "secret_newabcdef1234567890abcdef"
  }
}
```

### 2.6 获取应用调用日志

**接口地址**: `GET /apps/:id/logs`

**请求头**:
```
Authorization: Bearer <token>
```

**路径参数**:
- `id`: 应用ID

**响应示例**:
```json
{
  "code": 200,
  "message": "功能开发中"
}
```

---

## 3. 解题接口

### 3.1 解题

**接口地址**: `POST /solve/question`

**请求参数**:
```json
{
  "app_id": "app_1234567890ab",
  "app_secret": "secret_abcdef1234567890abcdef12",
  "image": "base64编码的图片数据",
  "question_type": "choice"
}
```

**参数说明**:
- `app_id`: 应用ID（必填）
- `app_secret`: 应用密钥（必填）
- `image`: base64编码的图片数据（必填）
- `question_type`: 题目类型，可选值：choice（选择题）、fill（填空题）、judge（判断题）等

**响应示例**:
```json
{
  "code": 200,
  "message": "解题成功",
  "data": {
    "question_id": 1,
    "question_text": "题目内容",
    "answer": "A",
    "explanation": "解题思路",
    "confidence": 0.95,
    "source": "cache",
    "cost_tokens": 100
  }
}
```

---

## 4. 使用示例

### 4.1 完整的用户注册登录流程

```javascript
// 1. 发送验证码
const smsResponse = await fetch('/api/v1/send-sms', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ phone: '13800138000' })
});

// 2. 用户注册
const registerResponse = await fetch('/api/v1/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    phone: '13800138000',
    password: '123456',
    nickname: '用户昵称',
    sms_code: '123456'
  })
});

// 3. 用户登录
const loginResponse = await fetch('/api/v1/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    phone: '13800138000',
    password: '123456'
  })
});

const loginData = await loginResponse.json();
const token = loginData.data.token;
```

### 4.2 应用创建和管理流程

```javascript
// 1. 创建应用
const createAppResponse = await fetch('/api/v1/apps', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({ name: '我的应用' })
});

const appData = await createAppResponse.json();
const { app_id, app_secret } = appData.data;

// 2. 获取应用列表
const appsResponse = await fetch('/api/v1/apps', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// 3. 使用应用进行解题
const solveResponse = await fetch('/api/v1/solve/question', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    app_id: app_id,
    app_secret: app_secret,
    image: 'base64编码的图片',
    question_type: 'choice'
  })
});
```

## 5. 注意事项

1. **认证令牌**: JWT令牌有效期为24小时，过期后需要重新登录
2. **应用状态**: 新注册用户默认为禁用状态，需要管理员激活后才能正常使用
3. **积分系统**: 解题会消耗用户积分，积分不足时无法使用解题功能
4. **应用密钥**: 应用密钥用于解题接口认证，请妥善保管
5. **图片格式**: 解题接口支持常见图片格式（JPG、PNG等），建议图片大小不超过5MB

## 6. 开发环境配置

- **服务器地址**: `http://localhost:8080`
- **数据库**: MySQL
- **缓存**: Redis
- **短信服务**: 开发环境会直接返回验证码，生产环境需要配置短信服务商
