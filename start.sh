#!/bin/bash

# Solve Go API 快速启动脚本
# 一键启动或重启服务

set -e

# 配置
SERVICE_NAME="solve-api"
SERVICE_PORT="8080"

# 颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Solve Go API 快速启动脚本 ===${NC}"

# 创建日志目录
mkdir -p logs

# 停止现有服务
echo -e "${YELLOW}[1/4]${NC} 停止现有服务..."
pkill -f "$SERVICE_NAME" 2>/dev/null || echo "没有运行中的服务"
sleep 1

# 检查环境
echo -e "${YELLOW}[2/4]${NC} 检查环境..."
if [ ! -f .env ]; then
    if [ -f .env.example ]; then
        cp .env.example .env
        echo "已创建.env文件，请根据需要修改配置"
    else
        echo -e "${RED}错误: 请创建.env文件${NC}"
        exit 1
    fi
fi

# 构建应用
echo -e "${YELLOW}[3/4]${NC} 构建应用..."
go mod download
go build -o "$SERVICE_NAME" main.go

# 启动服务
echo -e "${YELLOW}[4/4]${NC} 启动服务..."
nohup ./"$SERVICE_NAME" > logs/service.log 2>&1 &
SERVICE_PID=$!

# 等待启动
sleep 3

# 检查是否启动成功
if kill -0 $SERVICE_PID 2>/dev/null; then
    echo -e "${GREEN}✅ 服务启动成功!${NC}"
    echo -e "   PID: $SERVICE_PID"
    echo -e "   端口: $SERVICE_PORT"
    echo -e "   日志: logs/service.log"
    
    # 健康检查
    if command -v curl &> /dev/null; then
        sleep 2
        if curl -s http://localhost:$SERVICE_PORT/health >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 健康检查通过${NC}"
        else
            echo -e "${YELLOW}⚠️  健康检查失败，请查看日志${NC}"
        fi
    fi
    
    echo ""
    echo "常用命令:"
    echo "  查看状态: ./run.sh status"
    echo "  查看日志: ./run.sh logs"
    echo "  停止服务: ./run.sh stop"
    echo "  重启服务: ./run.sh restart"
else
    echo -e "${RED}❌ 服务启动失败，请查看日志: logs/service.log${NC}"
    exit 1
fi
