package router

import (
	"solve-go-api/internal/handlers"
	"solve-go-api/internal/middleware"
	"solve-go-api/internal/services"

	"github.com/gin-gonic/gin"
)

// Setup 设置路由
func Setup(container *services.Container) *gin.Engine {
	r := gin.Default()

	// 全局中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())

	// 创建处理器
	authHandler := handlers.NewAuthHandler(container.UserService, container.SMSService)
	solveHandler := handlers.NewSolveHandler(container.SolveService, container.AppService, container.LogService)
	appHandler := handlers.NewAppHandler(container.AppService, container.UserService)
	questionHandler := handlers.NewQuestionHandler(container.QuestionService, container.ParserService)
	logHandler := handlers.NewLogHandler(container.LogService)
	adminHandler := handlers.NewAdminHandler(container.UserService, container.AppService, container.QuestionService)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.J<PERSON>N(200, gin.H{"status": "ok"})
	})

	// API版本1
	v1 := r.Group("/api/v1")
	{
		// 公开接口
		public := v1.Group("/")
		{
			// 用户认证
			public.POST("/send-sms", authHandler.SendSMSCode)
			public.POST("/register", authHandler.Register)
			public.POST("/login", authHandler.Login)
			public.POST("/forgot-password", authHandler.ForgotPassword)
			public.POST("/reset-password", authHandler.ResetPassword)
		}

		// 核心解题API（需要应用认证）
		solve := v1.Group("/solve")
		solve.Use(middleware.AppAuth(container.AppService))
		{
			solve.POST("/question", solveHandler.SolveQuestion)
		}

		// 需要用户认证的接口
		auth := v1.Group("/")
		auth.Use(middleware.JWTAuth())
		{
			// 用户相关
			user := auth.Group("/user")
			{
				user.GET("/profile", authHandler.GetProfile)
				user.PUT("/profile", authHandler.UpdateProfile)
				user.POST("/change-password", authHandler.ChangePassword)
				user.GET("/balance-logs", authHandler.GetBalanceLogs)
			}

			// 应用管理
			apps := auth.Group("/apps")
			{
				apps.GET("/", appHandler.GetApps)
				apps.POST("/", appHandler.CreateApp)
				apps.PUT("/:id", appHandler.UpdateApp)
				apps.DELETE("/:id", appHandler.DeleteApp)
				apps.POST("/:id/reset-secret", appHandler.ResetSecret)
				apps.GET("/:id/logs", appHandler.GetAppLogs)
			}
		}

		// 题库管理员权限
		manager := v1.Group("/manager")
		manager.Use(middleware.JWTAuth())
		manager.Use(middleware.RoleAuth("manager", "admin"))
		{
			// 题库管理
			questions := manager.Group("/questions")
			{
				questions.GET("/", questionHandler.GetQuestions)
				questions.GET("/:id", questionHandler.GetQuestion)
				questions.PUT("/:id", questionHandler.UpdateQuestion)
				questions.DELETE("/:id", questionHandler.DeleteQuestion)
				questions.POST("/:id/verify", questionHandler.VerifyQuestion)
				questions.POST("/:id/unverify", questionHandler.UnverifyQuestion)
				questions.GET("/stats", questionHandler.GetStats)
			}

			// 日志管理
			logs := manager.Group("/logs")
			{
				logs.GET("/", logHandler.GetLogs)
				logs.GET("/:id", logHandler.GetLog)
				logs.DELETE("/:id", logHandler.DeleteLog)
				logs.GET("/stats", logHandler.GetStats)
			}
		}

		// 超级管理员权限
		admin := v1.Group("/admin")
		admin.Use(middleware.JWTAuth())
		admin.Use(middleware.RoleAuth("admin"))
		{
			// 用户管理
			users := admin.Group("/users")
			{
				users.GET("/", adminHandler.GetUsers)
				users.GET("/:id", adminHandler.GetUser)
				users.PUT("/:id", adminHandler.UpdateUser)
				users.POST("/:id/activate", adminHandler.ActivateUser)
				users.POST("/:id/deactivate", adminHandler.DeactivateUser)
				users.POST("/:id/recharge", adminHandler.RechargeUser)
				users.GET("/stats", adminHandler.GetUserStats)
				// 用户审核相关接口
				users.GET("/pending", adminHandler.GetPendingUsers)
				users.POST("/:id/approve", adminHandler.ApproveUser)
				users.POST("/:id/reject", adminHandler.RejectUser)
			}

			// 应用管理
			adminApps := admin.Group("/apps")
			{
				adminApps.GET("/", adminHandler.GetAllApps)
				adminApps.PUT("/:id", adminHandler.UpdateApp)
				adminApps.POST("/:id/activate", adminHandler.ActivateApp)
				adminApps.POST("/:id/deactivate", adminHandler.DeactivateApp)
			}

			// 系统配置
			config := admin.Group("/config")
			{
				config.GET("/", adminHandler.GetConfigs)
				config.PUT("/:key", adminHandler.UpdateConfig)
			}

			// 模型配置
			models := admin.Group("/models")
			{
				models.GET("/", adminHandler.GetModels)
				models.POST("/", adminHandler.CreateModel)
				models.PUT("/:id", adminHandler.UpdateModel)
				models.DELETE("/:id", adminHandler.DeleteModel)
			}
		}
	}

	return r
}
