package config

import (
	"os"
	"strconv"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Redis    RedisConfig
	SMS      SMSConfig
	Models   ModelsConfig
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string
	Mode string // debug, release, test
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string
	Port     string
	Username string
	Password string
	Database string
	Charset  string
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

// SMSConfig 短信配置
type SMSConfig struct {
	AccessKeyID     string
	AccessKeySecret string
	SignName        string
	TemplateCode    string
}

// ModelsConfig 模型配置
type ModelsConfig struct {
	OCRModel   string // 环境变量中配置的OCR模型名称
	SolveModel string // 环境变量中配置的Solve模型名称
}

// Load 加载配置
func Load() (*Config, error) {
	config := &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			// 注意：使用远程数据库配置，不要使用localhost
			// 默认值已设置为远程服务器配置，避免本地配置问题
			Host:     getEnv("DB_HOST", "***********"),      // 远程MySQL服务器
			Port:     getEnv("DB_PORT", "3380"),             // 远程MySQL端口
			Username: getEnv("DB_USERNAME", "gmdns"),        // 远程MySQL用户名
			Password: getEnv("DB_PASSWORD", "Suyan15913.."), // 远程MySQL密码
			Database: getEnv("DB_DATABASE", "solve_web"),    // 数据库名称
			Charset:  getEnv("DB_CHARSET", "utf8mb4"),
		},
		Redis: RedisConfig{
			// 注意：使用远程Redis配置，不要使用localhost
			// 默认值已设置为远程服务器配置，避免本地配置问题
			Host:     getEnv("REDIS_HOST", "***********"),      // 远程Redis服务器
			Port:     getEnv("REDIS_PORT", "6379"),             // 远程Redis端口
			Password: getEnv("REDIS_PASSWORD", "Suyan15913.."), // 远程Redis密码
			DB:       getEnvAsInt("REDIS_DB", 0),
		},
		SMS: SMSConfig{
			AccessKeyID:     getEnv("SMS_ACCESS_KEY_ID", ""),
			AccessKeySecret: getEnv("SMS_ACCESS_KEY_SECRET", ""),
			SignName:        getEnv("SMS_SIGN_NAME", ""),
			TemplateCode:    getEnv("SMS_TEMPLATE_CODE", ""),
		},
		Models: ModelsConfig{
			OCRModel:   getEnv("MODEL_OCR", "qwen-vl-plus"),
			SolveModel: getEnv("MODEL_SOLVE", "qwen-plus"),
		},
	}

	return config, nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
