package services

import (
	"context"
	"encoding/json"
	"solve-go-api/internal/models"
	"time"

	"github.com/redis/go-redis/v9"
)

// CacheService 缓存服务
type CacheService struct {
	rdb *redis.Client
}

// NewCacheService 创建缓存服务
func NewCacheService(rdb *redis.Client) *CacheService {
	return &CacheService{rdb: rdb}
}

// GetQuestionsByHashKey 根据哈希键获取题目
func (s *CacheService) GetQuestionsByHashKey(hashKey string) ([]models.QuestionResponse, error) {
	ctx := context.Background()

	result, err := s.rdb.Get(ctx, hashKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		return nil, err
	}

	var questions []models.QuestionResponse
	if err := json.Unmarshal([]byte(result), &questions); err != nil {
		return nil, err
	}

	return questions, nil
}

// SetQuestionsByHashKey 设置题目缓存
func (s *CacheService) SetQuestionsByHashKey(hashKey string, questions []models.QuestionResponse, expiration time.Duration) error {
	ctx := context.Background()

	data, err := json.Marshal(questions)
	if err != nil {
		return err
	}

	return s.rdb.Set(ctx, hashKey, data, expiration).Err()
}

// DeleteQuestionsByHashKey 删除题目缓存
func (s *CacheService) DeleteQuestionsByHashKey(hashKey string) error {
	ctx := context.Background()
	return s.rdb.Del(ctx, hashKey).Err()
}

// SetQuestionCache 设置题目缓存（默认24小时过期）
func (s *CacheService) SetQuestionCache(hashKey string, questions []models.QuestionResponse) error {
	return s.SetQuestionsByHashKey(hashKey, questions, 24*time.Hour)
}

// GetCacheStats 获取缓存统计信息
func (s *CacheService) GetCacheStats() (map[string]interface{}, error) {
	ctx := context.Background()

	info, err := s.rdb.Info(ctx, "memory").Result()
	if err != nil {
		return nil, err
	}

	dbSize, err := s.rdb.DBSize(ctx).Result()
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"db_size": dbSize,
		"info":    info,
	}

	return stats, nil
}
