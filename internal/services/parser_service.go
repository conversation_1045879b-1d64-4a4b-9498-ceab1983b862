package services

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"solve-go-api/internal/models"
	"strings"
)

// ParserService 解析服务
type ParserService struct{}

// NewParserService 创建解析服务
func NewParserService() *ParserService {
	return &ParserService{}
}

// ParseOCRResponse 解析OCR模型响应
func (s *ParserService) ParseOCRResponse(response *models.OCRResponse, userURL string) (*models.SaveQuestion, error) {
	if len(response.Output.Choices) == 0 {
		return nil, fmt.Errorf("no choices in OCR response")
	}

	content := response.Output.Choices[0].Message.Content

	// 🔍 调试日志：记录原始content类型和内容
	log.Printf("🔍 [OCR-DEBUG] Content类型: %T", content)

	// 处理不同类型的content
	var contentStr string
	switch v := content.(type) {
	case string:
		contentStr = v
		log.Printf("🔍 [OCR-DEBUG] String类型内容长度: %d", len(contentStr))
	case []interface{}:
		log.Printf("🔍 [OCR-DEBUG] Array类型，元素数量: %d", len(v))
		// 如果是数组，尝试提取文本内容
		for i, item := range v {
			log.Printf("🔍 [OCR-DEBUG] Array[%d]类型: %T", i, item)
			if itemMap, ok := item.(map[string]interface{}); ok {
				if text, exists := itemMap["text"]; exists {
					if textStr, ok := text.(string); ok {
						contentStr += textStr + "\n"
						log.Printf("🔍 [OCR-DEBUG] 提取文本: %s", textStr)
					}
				}
			}
		}
		contentStr = strings.TrimSpace(contentStr)
	default:
		log.Printf("🔍 [OCR-DEBUG] 未支持的类型: %T, 值: %+v", content, content)
		return nil, fmt.Errorf("unsupported content type: %T", content)
	}

	// 🔍 调试日志：记录最终提取的内容
	log.Printf("🔍 [OCR-DEBUG] 最终内容长度: %d", len(contentStr))
	log.Printf("🔍 [OCR-DEBUG] 最终内容前200字符: %.200s", contentStr)
	if len(contentStr) > 200 {
		log.Printf("🔍 [OCR-DEBUG] 最终内容后200字符: %s", contentStr[len(contentStr)-200:])
	}

	// 清理和提取JSON内容
	jsonStr := s.extractJSONFromContent(contentStr)

	// 🔧 字段名映射：将 "question" 转换为 "qutext"
	mappedJsonStr := s.mapOCRFields(jsonStr)
	log.Printf("🔍 [OCR-DEBUG] 字段映射后的JSON: %s", mappedJsonStr)

	// 尝试解析JSON格式的响应
	var ocrData models.OCRParsedData
	if err := json.Unmarshal([]byte(mappedJsonStr), &ocrData); err != nil {
		log.Printf("❌ [OCR-PARSE] JSON解析失败: %v", err)
		log.Printf("❌ [OCR-PARSE] 失败的JSON内容: %s", mappedJsonStr)
		return nil, fmt.Errorf("failed to parse OCR response JSON: %w", err)
	}

	log.Printf("✅ [OCR-PARSE] JSON解析成功")
	log.Printf("📋 [OCR-PARSE] 解析结果 - QuText: %s", ocrData.QuText)
	log.Printf("📋 [OCR-PARSE] 解析结果 - Options数量: %d", len(ocrData.Options))
	for key, value := range ocrData.Options {
		log.Printf("📋 [OCR-PARSE] 选项 %s: %s", key, value)
	}

	// 检测题目类型
	log.Printf("🔍 [TYPE-DETECT] 开始检测题目类型，题干内容: %s", ocrData.QuText)
	questionType := s.detectQuestionType(ocrData.QuText)
	log.Printf("🔍 [TYPE-DETECT] 检测结果: %s", questionType)
	if questionType == "" {
		log.Printf("❌ [TYPE-DETECT] 题目类型检测失败，无法识别题目类型")
		return nil, fmt.Errorf("图片解析失败，请重新拍摄标准图片")
	}

	// 清洗题干内容（二次提纯：先清除前缀，再清理标点符号）
	log.Printf("🧹 [DATA-CLEAN] 开始清洗题干内容（二次提纯）")
	log.Printf("🧹 [DATA-CLEAN] 原始题干: %s", ocrData.QuText)
	cleanedContent := s.cleanQuestionContent(ocrData.QuText)
	log.Printf("🧹 [DATA-CLEAN] 二次提纯完成，最终题干: %s", cleanedContent)

	// 清洗选项
	log.Printf("🧹 [DATA-CLEAN] 开始清洗选项内容")
	cleanedOptions := s.cleanOptions(ocrData.Options)
	for key, value := range cleanedOptions {
		log.Printf("🧹 [DATA-CLEAN] 清洗后选项 %s: %s", key, value)
	}

	// 计算字符长度
	questionLen := len([]rune(cleanedContent))
	log.Printf("📏 [DATA-CALC] 题干字符长度: %d", questionLen)

	// 生成哈希键
	hashKey := s.generateHashKey(cleanedContent)
	log.Printf("🔑 [DATA-CALC] 生成哈希键: %s", hashKey)

	// 构建SaveQuestion对象
	saveQuestion := &models.SaveQuestion{
		Type:         questionType,
		Content:      ocrData.QuText,
		CleanContent: cleanedContent,
		Options:      cleanedOptions,
		UserURL:      userURL,
		QuestionLen:  questionLen,
		HashKey:      hashKey,
	}

	// 记录最终构建的SaveQuestion对象信息
	log.Printf("📦 [SAVE-QUESTION] 构建完成")
	log.Printf("📦 [SAVE-QUESTION] Type: %s", saveQuestion.Type)
	log.Printf("📦 [SAVE-QUESTION] Content: %s", saveQuestion.Content)
	log.Printf("📦 [SAVE-QUESTION] CleanContent: %s", saveQuestion.CleanContent)
	log.Printf("📦 [SAVE-QUESTION] UserURL: %s", saveQuestion.UserURL)
	log.Printf("📦 [SAVE-QUESTION] QuestionLen: %d", saveQuestion.QuestionLen)
	log.Printf("📦 [SAVE-QUESTION] HashKey: %s", saveQuestion.HashKey)
	log.Printf("📦 [SAVE-QUESTION] Options数量: %d", len(saveQuestion.Options))

	return saveQuestion, nil
}

// ParseSolveResponse 解析Solve模型响应
func (s *ParserService) ParseSolveResponse(response *models.SolveResponse) (*models.SolveParsedData, error) {
	if len(response.Output.Choices) == 0 {
		return nil, fmt.Errorf("no choices in solve response")
	}

	content := response.Output.Choices[0].Message.Content

	// 尝试解析JSON格式的响应
	var solveData models.SolveParsedData
	if err := json.Unmarshal([]byte(content), &solveData); err != nil {
		return nil, fmt.Errorf("failed to parse solve response JSON: %w", err)
	}

	return &solveData, nil
}

// detectQuestionType 检测题目类型
func (s *ParserService) detectQuestionType(content string) string {
	log.Printf("🔍 [TYPE-DETECT] 原始内容: %s", content)
	content = strings.ToLower(content)
	log.Printf("🔍 [TYPE-DETECT] 转换小写后: %s", content)

	if strings.Contains(content, "单选题") {
		log.Printf("🔍 [TYPE-DETECT] 检测到单选题")
		return "单选题"
	}
	if strings.Contains(content, "多选题") {
		log.Printf("🔍 [TYPE-DETECT] 检测到多选题")
		return "多选题"
	}
	if strings.Contains(content, "判断题") {
		log.Printf("🔍 [TYPE-DETECT] 检测到判断题")
		return "判断题"
	}
	if strings.Contains(content, "填空题") {
		log.Printf("🔍 [TYPE-DETECT] 检测到填空题")
		return "填空题"
	}

	log.Printf("🔍 [TYPE-DETECT] 未检测到任何题目类型")
	return ""
}

// cleanQuestionContent 清洗题干内容
func (s *ParserService) cleanQuestionContent(content string) string {
	// 第一步：使用正则表达式清洗前缀
	re := regexp.MustCompile(`^(（?\(?\s*(单选题|多选题|判断题|填空题)?\)?）?\s*\d{1,3}[、.．]?\s*)`)
	cleaned := re.ReplaceAllString(content, "")
	log.Printf("🧹 [DATA-CLEAN] 第一步清除前缀后: %s", cleaned)

	// 第二步：清洗标点符号、空格、换行符（包含中文标点符号）
	punctRe := regexp.MustCompile(`[[:punct:]\s，。？！；：""''（）【】《》、]+`)
	cleaned = punctRe.ReplaceAllString(cleaned, "")
	log.Printf("🧹 [DATA-CLEAN] 第二步清理标点后: %s", cleaned)

	return cleaned
}

// cleanOptions 清洗选项
func (s *ParserService) cleanOptions(options map[string]string) map[string]string {
	cleaned := make(map[string]string)
	// 清洗标点符号、空格、换行符（包含中文标点符号）
	punctRe := regexp.MustCompile(`[[:punct:]\s，。？！；：""''（）【】《》、]+`)

	for key, value := range options {
		cleanedValue := punctRe.ReplaceAllString(value, "")
		cleaned[key] = cleanedValue
		log.Printf("🧹 [DATA-CLEAN] 选项 %s 清理标点前: %s, 清理后: %s", key, value, cleanedValue)
	}

	return cleaned
}

// generateHashKey 生成哈希键
func (s *ParserService) generateHashKey(content string) string {
	hash := md5.Sum([]byte(content))
	return fmt.Sprintf("%x", hash)
}

// ConvertToQuestionResponse 转换为API响应格式
func (s *ParserService) ConvertToQuestionResponse(questions []models.QuestionBank) []models.QuestionResponse {
	responses := make([]models.QuestionResponse, 0, len(questions))

	for _, q := range questions {
		// 解析选项
		var options map[string]string
		if err := json.Unmarshal(q.Options, &options); err != nil {
			options = make(map[string]string)
		}

		// 解析答案
		var answer interface{}
		if err := json.Unmarshal(q.Answer, &answer); err != nil {
			answer = ""
		}

		// 转换题目类型
		typeMap := map[int]string{
			1: "判断题",
			2: "单选题",
			3: "多选题",
		}

		response := models.QuestionResponse{
			ID:       q.ID,
			Type:     typeMap[q.Type],
			Content:  q.Content,
			Options:  options,
			Answer:   answer,
			Analysis: q.Analysis,
			ImageURL: q.ImageURL,
			UserURL:  q.UserURL,
		}

		responses = append(responses, response)
	}

	return responses
}

// GetQuestionTypeCode 获取题目类型代码
func (s *ParserService) GetQuestionTypeCode(questionType string) int {
	switch questionType {
	case "判断题":
		return 1
	case "单选题":
		return 2
	case "多选题":
		return 3
	default:
		return 0
	}
}

// extractJSONFromContent 从内容中提取JSON
func (s *ParserService) extractJSONFromContent(content string) string {
	// 🔍 调试日志：记录JSON提取过程
	log.Printf("🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: %s", content)

	// 如果内容包含代码块标记，尝试提取
	if strings.Contains(content, "```") {
		// 查找JSON代码块
		jsonStart := strings.Index(content, "```json")
		if jsonStart != -1 {
			jsonStart += 7 // 跳过 "```json"
			jsonEnd := strings.Index(content[jsonStart:], "```")
			if jsonEnd != -1 {
				extracted := strings.TrimSpace(content[jsonStart : jsonStart+jsonEnd])
				log.Printf("🔍 [JSON-EXTRACT] 从```json```中提取: %s", extracted)
				return extracted
			}
		}

		// 查找普通代码块
		codeStart := strings.Index(content, "```")
		if codeStart != -1 {
			codeStart += 3 // 跳过 "```"
			// 跳过可能的语言标识符
			if newlinePos := strings.Index(content[codeStart:], "\n"); newlinePos != -1 {
				codeStart += newlinePos + 1
			}
			codeEnd := strings.Index(content[codeStart:], "```")
			if codeEnd != -1 {
				extracted := strings.TrimSpace(content[codeStart : codeStart+codeEnd])
				log.Printf("🔍 [JSON-EXTRACT] 从```代码块中提取: %s", extracted)
				return extracted
			}
		}
	}

	// 如果没有代码块，直接返回原内容
	log.Printf("🔍 [JSON-EXTRACT] 未找到代码块，返回原内容")
	return content
}

// mapOCRFields 映射OCR字段名
func (s *ParserService) mapOCRFields(jsonStr string) string {
	// 🔍 调试日志：记录字段映射过程
	log.Printf("🔍 [FIELD-MAP] 开始字段映射，原始JSON: %s", jsonStr)

	// 将 "question" 替换为 "qutext"
	mappedStr := strings.ReplaceAll(jsonStr, `"question"`, `"qutext"`)

	log.Printf("🔍 [FIELD-MAP] 映射完成，结果: %s", mappedStr)
	return mappedStr
}
