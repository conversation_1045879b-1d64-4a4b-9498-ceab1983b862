package services

import (
	"encoding/json"
	"log"
	"solve-go-api/internal/models"
	"sort"

	"gorm.io/gorm"
)

// MatchService 题目匹配服务
type MatchService struct {
	db *gorm.DB
}

// CandidateWithSimilarity 带相似度的候选题目
type CandidateWithSimilarity struct {
	Question   models.QuestionBank
	Similarity float64
}

// NewMatchService 创建匹配服务
func NewMatchService(db *gorm.DB) *MatchService {
	return &MatchService{db: db}
}

// MatchQuestion 根据题目信息匹配数据库中的题目（三步筛选策略）
func (s *MatchService) MatchQuestion(saveQuestion *models.SaveQuestion) ([]models.QuestionBank, error) {
	log.Printf("🔍 [FUZZY-MATCH] 开始三步筛选匹配")

	// 第一步：MySQL数据库筛选 - verified = true、type一致、question_len ±5
	typeCode := s.getQuestionTypeCode(saveQuestion.Type)
	if typeCode == 0 {
		log.Printf("🔍 [STEP-1] 未知题目类型: %s", saveQuestion.Type)
		return nil, nil
	}

	var candidates []models.QuestionBank
	err := s.db.Where("verified = ? AND type = ? AND ABS(question_len - ?) <= 5",
		true, typeCode, saveQuestion.QuestionLen).Find(&candidates).Error
	if err != nil {
		log.Printf("🔍 [STEP-1] 数据库查询失败: %v", err)
		return nil, err
	}

	log.Printf("🔍 [STEP-1] MySQL筛选完成 - 找到%d个候选题目", len(candidates))
	if len(candidates) == 0 {
		return nil, nil
	}

	// 第二步：编辑距离筛选 - 取相似度最高的3个
	top3Candidates := s.filterByEditDistance(candidates, saveQuestion.CleanContent, 3)
	log.Printf("🔍 [STEP-2] 编辑距离筛选完成 - 取前3个最相似的题目")
	if len(top3Candidates) == 0 {
		return nil, nil
	}

	// 第三步：选项交集筛选 - ≥3个选项匹配
	finalResults := s.filterByOptions(top3Candidates, saveQuestion.Options)
	log.Printf("🔍 [STEP-3] 选项交集筛选完成 - 最终匹配%d个题目", len(finalResults))

	return finalResults, nil
}

// getQuestionTypeCode 获取题目类型代码
func (s *MatchService) getQuestionTypeCode(questionType string) int {
	switch questionType {
	case "判断题":
		return 1
	case "单选题":
		return 2
	case "多选题":
		return 3
	default:
		return 0
	}
}

// filterByEditDistance 编辑距离筛选，取相似度最高的topN个
func (s *MatchService) filterByEditDistance(candidates []models.QuestionBank, targetContent string, topN int) []models.QuestionBank {
	var candidatesWithSim []CandidateWithSimilarity

	log.Printf("🔍 [EDIT-DISTANCE] 开始计算%d个候选题目的相似度", len(candidates))

	for i, candidate := range candidates {
		similarity := s.calculateSimilarity(candidate.ContentClean, targetContent)

		log.Printf("🔍 [EDIT-DISTANCE] 候选%d - 相似度: %.3f, 内容: %s", i+1, similarity, candidate.ContentClean)

		// 设置相似度阈值，比如0.6（60%相似度）
		if similarity >= 0.6 {
			candidatesWithSim = append(candidatesWithSim, CandidateWithSimilarity{
				Question:   candidate,
				Similarity: similarity,
			})
		}
	}

	// 按相似度降序排序
	sort.Slice(candidatesWithSim, func(i, j int) bool {
		return candidatesWithSim[i].Similarity > candidatesWithSim[j].Similarity
	})

	// 取前topN个
	var result []models.QuestionBank
	for i := 0; i < len(candidatesWithSim) && i < topN; i++ {
		result = append(result, candidatesWithSim[i].Question)
		log.Printf("🔍 [EDIT-DISTANCE] 选中候选%d - 相似度: %.3f", i+1, candidatesWithSim[i].Similarity)
	}

	return result
}

// filterByOptions 根据选项交集筛选
func (s *MatchService) filterByOptions(candidates []models.QuestionBank, userOptions map[string]string) []models.QuestionBank {
	var filtered []models.QuestionBank

	for _, candidate := range candidates {
		// 解析数据库中的选项
		var dbOptions map[string]string
		if err := json.Unmarshal(candidate.Options, &dbOptions); err != nil {
			continue // 跳过解析失败的记录
		}

		// 计算选项交集
		intersectionCount := s.calculateOptionsIntersection(userOptions, dbOptions)

		// 如果命中>=3个选项，则认为匹配
		if intersectionCount >= 3 {
			filtered = append(filtered, candidate)
		}
	}

	return filtered
}

// calculateOptionsIntersection 计算选项交集数量
func (s *MatchService) calculateOptionsIntersection(userOptions, dbOptions map[string]string) int {
	intersectionCount := 0

	for key, userValue := range userOptions {
		if dbValue, exists := dbOptions[key]; exists && userValue == dbValue {
			intersectionCount++
		}
	}

	return intersectionCount
}

// calculateSimilarity 计算字符串相似度
func (s *MatchService) calculateSimilarity(s1, s2 string) float64 {
	distance := s.levenshteinDistance(s1, s2)
	maxLen := maxTwo(len([]rune(s1)), len([]rune(s2)))
	if maxLen == 0 {
		return 1.0
	}
	return 1.0 - float64(distance)/float64(maxLen)
}

// levenshteinDistance 计算编辑距离（Levenshtein Distance）
func (s *MatchService) levenshteinDistance(s1, s2 string) int {
	r1, r2 := []rune(s1), []rune(s2)
	len1, len2 := len(r1), len(r2)

	// 创建矩阵
	matrix := make([][]int, len1+1)
	for i := range matrix {
		matrix[i] = make([]int, len2+1)
	}

	// 初始化第一行和第一列
	for i := 0; i <= len1; i++ {
		matrix[i][0] = i
	}
	for j := 0; j <= len2; j++ {
		matrix[0][j] = j
	}

	// 填充矩阵
	for i := 1; i <= len1; i++ {
		for j := 1; j <= len2; j++ {
			cost := 0
			if r1[i-1] != r2[j-1] {
				cost = 1
			}

			matrix[i][j] = minThree(
				matrix[i-1][j]+1,      // 删除
				matrix[i][j-1]+1,      // 插入
				matrix[i-1][j-1]+cost, // 替换
			)
		}
	}

	return matrix[len1][len2]
}

// minThree 返回三个数中的最小值
func minThree(a, b, c int) int {
	if a < b {
		if a < c {
			return a
		}
		return c
	}
	if b < c {
		return b
	}
	return c
}

// maxTwo 返回两个数中的最大值
func maxTwo(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// abs 计算绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}
