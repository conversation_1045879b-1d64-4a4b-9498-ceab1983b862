package services

import (
	"solve-go-api/internal/models"
	"strconv"

	"gorm.io/gorm"
)

// LogService 日志服务
type LogService struct {
	db *gorm.DB
}

// NewLogService 创建日志服务
func NewLogService(db *gorm.DB) *LogService {
	return &LogService{db: db}
}

// CreateSolveLog 创建解题日志
func (s *LogService) CreateSolveLog(ctx *models.ProcessContext) error {
	var matchedID uint
	if ctx.SaveQuestion != nil && ctx.SaveQuestion.HashKey != "" {
		// 尝试解析匹配的题目ID
		if id, err := strconv.ParseUint(ctx.SaveQuestion.HashKey, 10, 32); err == nil {
			matchedID = uint(id)
		}
	}

	log := &models.SolveLog{
		AppID:     ctx.AppID,
		UserURL:   ctx.UserURL,
		MatchedID: matchedID,
		OCRToken:  ctx.OCRToken,
		Source:    ctx.Source,
		Data:      ctx.Response,
		Status:    ctx.Status,
		Latency:   ctx.GetLatency(),
	}

	return s.db.Create(log).Error
}

// GetSolveLogs 获取解题日志列表
func (s *LogService) GetSolveLogs(page, pageSize int, filters map[string]interface{}) ([]models.SolveLog, int64, error) {
	var logs []models.SolveLog
	var total int64

	query := s.db.Model(&models.SolveLog{}).Preload("App").Preload("Question")

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "app_id":
			query = query.Where("app_id = ?", value)
		case "status":
			query = query.Where("status = ?", value)
		case "source":
			query = query.Where("source = ?", value)
		case "start_date":
			query = query.Where("created_at >= ?", value)
		case "end_date":
			query = query.Where("created_at <= ?", value)
		}
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetSolveLogByID 根据ID获取解题日志
func (s *LogService) GetSolveLogByID(id uint) (*models.SolveLog, error) {
	var log models.SolveLog
	err := s.db.Preload("App").Preload("Question").First(&log, id).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// DeleteSolveLog 删除解题日志
func (s *LogService) DeleteSolveLog(id uint) error {
	return s.db.Delete(&models.SolveLog{}, id).Error
}

// GetSolveLogStats 获取解题日志统计
func (s *LogService) GetSolveLogStats(filters map[string]interface{}) (map[string]interface{}, error) {
	query := s.db.Model(&models.SolveLog{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "app_id":
			query = query.Where("app_id = ?", value)
		case "start_date":
			query = query.Where("created_at >= ?", value)
		case "end_date":
			query = query.Where("created_at <= ?", value)
		}
	}

	var total int64
	var success int64
	var failed int64

	// 总请求数
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 成功请求数
	err = query.Where("status = ?", 1).Count(&success).Error
	if err != nil {
		return nil, err
	}

	// 失败请求数
	err = query.Where("status = ?", 0).Count(&failed).Error
	if err != nil {
		return nil, err
	}

	// 按来源统计
	var sourceStats []map[string]interface{}
	err = s.db.Model(&models.SolveLog{}).
		Select("source, COUNT(*) as count").
		Group("source").
		Scan(&sourceStats).Error
	if err != nil {
		return nil, err
	}

	// 平均响应时间
	var avgLatency float64
	err = s.db.Model(&models.SolveLog{}).
		Select("AVG(latency) as avg_latency").
		Where("status = ?", 1).
		Scan(&avgLatency).Error
	if err != nil {
		return nil, err
	}

	// 总token消耗
	var totalTokens int64
	err = s.db.Model(&models.SolveLog{}).
		Select("SUM(ocr_token) as total_tokens").
		Scan(&totalTokens).Error
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"total":        total,
		"success":      success,
		"failed":       failed,
		"success_rate": float64(success) / float64(total) * 100,
		"by_source":    sourceStats,
		"avg_latency":  avgLatency,
		"total_tokens": totalTokens,
	}

	return stats, nil
}

// GetUserSolveLogs 获取用户的解题日志
func (s *LogService) GetUserSolveLogs(userID uint, page, pageSize int) ([]models.SolveLog, int64, error) {
	var logs []models.SolveLog
	var total int64

	// 通过应用关联查询用户的日志
	query := s.db.Model(&models.SolveLog{}).
		Joins("JOIN hook_apps ON hook_solve_logs.app_id = hook_apps.app_id").
		Where("hook_apps.user_id = ?", userID).
		Preload("App")

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("hook_solve_logs.created_at DESC").Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}
