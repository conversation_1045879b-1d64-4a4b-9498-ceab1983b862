package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Phone     string    `json:"phone" gorm:"type:varchar(20);uniqueIndex;not null;comment:手机号"`
	Password  string    `json:"-" gorm:"type:varchar(255);not null;comment:密码"`
	Role      string    `json:"role" gorm:"type:enum('admin','manager','user');default:'user';comment:角色"`
	Nickname  string    `json:"nickname" gorm:"type:varchar(100);comment:昵称"`
	Balance   int64     `json:"balance" gorm:"default:0;comment:积分余额"`
	IsActive  int       `json:"is_active" gorm:"type:tinyint;not null;default:2;comment:用户状态:0=禁用,1=正常,2=审核中"`
	CreatedAt time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time `json:"updated_at" gorm:"comment:最后登录时间"`
}

// 用户状态常量
const (
	UserStatusDisabled = 0 // 禁用
	UserStatusActive   = 1 // 正常
	UserStatusPending  = 2 // 审核中
)

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

// App 应用模型
type App struct {
	ID         uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID     uint      `json:"user_id" gorm:"not null;index;comment:关联用户ID"`
	Name       string    `json:"name" gorm:"type:varchar(100);not null;comment:应用名称"`
	AppID      string    `json:"app_id" gorm:"type:varchar(16);uniqueIndex;not null;comment:应用ID"`
	AppSecret  string    `json:"app_secret" gorm:"type:varchar(32);not null;comment:应用私钥"`
	Status     int       `json:"status" gorm:"default:0;comment:应用状态 0正常 1禁用"`
	TotalCalls uint64    `json:"total_calls" gorm:"default:0;comment:累计调用次数"`
	CreatedAt  time.Time `json:"created_at" gorm:"comment:创建时间"`
}

// TableName 指定表名
func (App) TableName() string {
	return "hook_apps"
}

// BalanceLog 积分变动记录
type BalanceLog struct {
	ID           uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID       uint      `json:"user_id" gorm:"not null;index;comment:关联用户ID"`
	ChangeAmount int64     `json:"change_amount" gorm:"not null;comment:变化金额"`
	Reason       string    `json:"reason" gorm:"type:varchar(255);comment:变化原因"`
	OperatorID   uint      `json:"operator_id" gorm:"comment:操作人ID"`
	CreatedAt    time.Time `json:"created_at" gorm:"comment:发生时间"`
}

// TableName 指定表名
func (BalanceLog) TableName() string {
	return "hook_balance_logs"
}

// SystemConfig 系统配置
type SystemConfig struct {
	ID          uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	Key         string    `json:"key" gorm:"type:varchar(100);uniqueIndex;not null;comment:配置键名"`
	Value       string    `json:"value" gorm:"type:text;comment:配置值"`
	Description string    `json:"description" gorm:"type:varchar(255);comment:配置描述"`
	CreatedAt   time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"comment:更新时间"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "hook_system_config"
}
