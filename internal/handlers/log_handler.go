package handlers

import (
	"net/http"
	"solve-go-api/internal/services"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// LogHandler 日志处理器
type LogHandler struct {
	logService *services.LogService
}

// NewLogHandler 创建日志处理器
func NewLogHandler(logService *services.LogService) *LogHandler {
	return &LogHandler{logService: logService}
}

// GetLogs 获取日志列表
func (h *LogHandler) GetLogs(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "20"))

	// 构建过滤条件
	filters := make(map[string]interface{})

	if appID := c.Query("app_id"); appID != "" {
		filters["app_id"] = appID
	}

	if status := c.Query("status"); status != "" {
		if statusInt, err := strconv.Atoi(status); err == nil {
			filters["status"] = statusInt
		}
	}

	if source := c.Query("source"); source != "" {
		filters["source"] = source
	}

	if startDate := c.Query("start_date"); startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			filters["start_date"] = t
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			// 设置为当天结束时间
			filters["end_date"] = t.Add(24*time.Hour - time.Second)
		}
	}

	logs, total, err := h.logService.GetSolveLogs(page, pageSize, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取日志失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"list":      logs,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetLog 获取单个日志
func (h *LogHandler) GetLog(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的日志ID",
		})
		return
	}

	log, err := h.logService.GetSolveLogByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "日志不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    log,
	})
}

// DeleteLog 删除日志
func (h *LogHandler) DeleteLog(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的日志ID",
		})
		return
	}

	err = h.logService.DeleteSolveLog(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除日志失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "日志删除成功",
	})
}

// GetStats 获取日志统计
func (h *LogHandler) GetStats(c *gin.Context) {
	// 构建过滤条件
	filters := make(map[string]interface{})

	if appID := c.Query("app_id"); appID != "" {
		filters["app_id"] = appID
	}

	if startDate := c.Query("start_date"); startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			filters["start_date"] = t
		}
	}

	if endDate := c.Query("end_date"); endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			filters["end_date"] = t.Add(24*time.Hour - time.Second)
		}
	}

	stats, err := h.logService.GetSolveLogStats(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取统计信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    stats,
	})
}
