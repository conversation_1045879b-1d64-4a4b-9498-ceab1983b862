package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"solve-go-api/internal/models"
	"solve-go-api/internal/services"

	"github.com/gin-gonic/gin"
)

// SolveHandler 解题处理器
type SolveHandler struct {
	solveService *services.SolveService
	appService   *services.AppService
	logService   *services.LogService
}

// NewSolveHandler 创建解题处理器
func NewSolveHandler(solveService *services.SolveService, appService *services.AppService, logService *services.LogService) *SolveHandler {
	return &SolveHandler{
		solveService: solveService,
		appService:   appService,
		logService:   logService,
	}
}

// SolveQuestion 解题接口
func (h *SolveHandler) SolveQuestion(c *gin.Context) {
	log.Printf("🎯 [HANDLER] 收到解题请求 - IP: %s, UserAgent: %s", c.ClientIP(), c.<PERSON>("User-Agent"))

	// 从中间件获取应用和用户信息
	app, exists := c.Get("app")
	if !exists {
		log.Printf("❌ [HANDLER] 无法获取应用信息")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    500,
			Message: "应用信息获取失败",
		})
		return
	}

	user, exists := c.Get("user")
	if !exists {
		log.Printf("❌ [HANDLER] 无法获取用户信息")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    500,
			Message: "用户信息获取失败",
		})
		return
	}

	var request models.APIRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("❌ [HANDLER] 请求参数绑定失败: %v", err)
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}
	log.Printf("✅ [HANDLER] 请求参数解析成功 - AppID: %s, ImageURL: %s", request.AppID, request.ImageURL)

	// 处理解题请求
	log.Printf("🚀 [HANDLER] 开始处理解题请求")
	ctx := h.solveService.ProcessSolveRequest(&request)

	// 设置应用和用户信息
	ctx.App = app.(*models.App)
	ctx.User = user.(*models.User)

	// 记录请求日志
	log.Printf("📝 [HANDLER] 记录解题日志")
	if err := h.logService.CreateSolveLog(ctx); err != nil {
		log.Printf("⚠️ [HANDLER] 日志记录失败: %v", err)
		// 日志记录失败不影响主流程，只记录错误
		// 可以考虑使用异步日志记录
	}

	// 如果成功，增加调用次数并扣除积分
	if ctx.Status == 1 {
		log.Printf("🎉 [HANDLER] 解题处理成功")
		// 增加应用调用次数
		h.appService.IncrementCallCount(ctx.AppID)

		// 扣除用户积分 (OCR token * 2)
		if ctx.OCRToken > 0 {
			deductAmount := int64(ctx.OCRToken * 2)
			h.appService.DeductUserBalance(
				ctx.User.ID,
				deductAmount,
				"API调用扣费",
				0, // 系统操作
			)
		}

		// 解析响应数据
		var responseData []models.QuestionResponse
		if err := json.Unmarshal(ctx.Response, &responseData); err == nil {
			c.JSON(http.StatusOK, models.APIResponse{
				Code:    200,
				Message: "success",
				Data:    responseData,
			})
		} else {
			c.JSON(http.StatusOK, models.APIResponse{
				Code:    200,
				Message: "success",
				Data:    json.RawMessage(ctx.Response),
			})
		}
	} else {
		// 请求失败
		log.Printf("❌ [HANDLER] 解题处理失败 - Status: %d, Message: %s", ctx.Status, ctx.Message)
		c.JSON(http.StatusOK, models.APIResponse{
			Code:    400,
			Message: ctx.Message,
		})
	}
}
