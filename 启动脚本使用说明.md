# Solve Go API 启动脚本使用说明

## 概述

为了简化服务的启动和管理，我创建了两个脚本：

1. **`start.sh`** - 一键快速启动脚本（推荐日常使用）
2. **`run.sh`** - 完整的服务管理脚本（提供更多功能）

## 快速开始

### 一键启动（最简单）

```bash
./start.sh
```

这个脚本会自动：
- 停止现有服务
- 检查环境配置
- 构建应用
- 启动服务
- 进行健康检查

### 完整管理

```bash
# 查看帮助
./run.sh help

# 启动服务
./run.sh start

# 停止服务
./run.sh stop

# 重启服务
./run.sh restart

# 查看状态
./run.sh status

# 查看日志
./run.sh logs

# 构建应用
./run.sh build
```

## 脚本功能

### start.sh（快速启动）
- ✅ 一键启动/重启
- ✅ 自动环境检查
- ✅ 自动构建
- ✅ 健康检查
- ✅ 简洁的输出信息

### run.sh（完整管理）
- ✅ 启动/停止/重启服务
- ✅ 服务状态检查
- ✅ 实时日志查看
- ✅ 端口占用检查
- ✅ 进程管理
- ✅ 健康检查
- ✅ 详细的错误处理

## 使用场景

### 日常开发
```bash
# 快速启动开发环境
./start.sh
```

### 服务管理
```bash
# 查看服务状态
./run.sh status

# 查看实时日志
./run.sh logs

# 重启服务
./run.sh restart
```

### 问题排查
```bash
# 检查服务状态
./run.sh status

# 查看日志
./run.sh logs

# 重新构建并启动
./run.sh build
./run.sh restart
```

## 文件说明

### 生成的文件
- `solve-api` - 编译后的二进制文件
- `logs/service.log` - 服务运行日志
- `logs/service.pid` - 服务进程ID文件（run.sh使用）

### 环境文件
- `.env` - 环境配置文件（如不存在会从.env.example复制）

## 常见问题

### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :8080

# 停止服务
./run.sh stop

# 或强制停止
pkill -f solve-api
```

### 2. 服务启动失败
```bash
# 查看详细日志
./run.sh logs

# 或直接查看日志文件
tail -f logs/service.log
```

### 3. 环境配置问题
```bash
# 检查.env文件是否存在
ls -la .env

# 编辑环境配置
nano .env
```

### 4. 构建失败
```bash
# 手动构建
go mod download
go build -o solve-api main.go

# 或使用脚本
./run.sh build
```

## 脚本特性

### 安全特性
- 优雅停止进程（先发送TERM信号，超时后使用KILL）
- 端口占用检查
- 环境配置验证
- 错误处理和回滚

### 便利特性
- 彩色输出
- 进度提示
- 健康检查
- 自动创建必要目录
- PID文件管理

### 兼容性
- 支持macOS和Linux
- 自动检测可用工具
- 优雅降级（某些功能不可用时仍能工作）

## 高级用法

### 后台运行
脚本默认在后台运行服务，如需前台运行：
```bash
# 直接运行（前台）
./solve-api

# 或使用go run（开发模式）
go run main.go
```

### 自定义配置
可以修改脚本顶部的配置变量：
```bash
SERVICE_NAME="solve-api"    # 服务名称
SERVICE_PORT="8080"         # 服务端口
LOG_FILE="logs/service.log" # 日志文件
PID_FILE="logs/service.pid" # PID文件
```

### 集成到系统服务
可以将脚本集成到systemd或其他服务管理器中。

## 故障排除

### 权限问题
```bash
chmod +x start.sh
chmod +x run.sh
```

### Go环境问题
```bash
# 检查Go版本
go version

# 检查Go环境
go env
```

### 网络问题
```bash
# 配置Go代理（如果下载依赖慢）
go env -w GOPROXY=https://goproxy.cn,direct
```

## 总结

- **日常使用**: 直接运行 `./start.sh` 即可
- **服务管理**: 使用 `./run.sh` 的各种命令
- **问题排查**: 使用 `./run.sh status` 和 `./run.sh logs`

这两个脚本让您可以在任何情况下快速启动或重启服务，无需记忆复杂的命令。
