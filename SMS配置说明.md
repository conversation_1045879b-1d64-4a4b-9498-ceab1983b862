# SMS短信服务配置说明

## 问题解决

短信发送接口 `POST /api/v1/send-sms` 已修复并正常工作。

### 修复的问题

1. **环境变量名称错误**
   - 原配置：`SMS_ACCESS_KEY` 和 `SMS_SECRET_KEY`
   - 正确配置：`SMS_ACCESS_KEY_ID` 和 `SMS_ACCESS_KEY_SECRET`

2. **缺少开发模式支持**
   - 添加了开发模式，当SMS配置为空时自动模拟短信发送
   - 避免了在开发环境中需要真实短信服务配置的问题

## 当前工作模式

### 开发模式（当前）
当 `.env` 文件中的SMS配置为空时，系统自动进入开发模式：
- 模拟短信发送成功
- 在服务日志中显示模拟发送信息
- 返回生成的验证码（便于开发测试）

### 生产模式
当配置了完整的阿里云短信服务参数时，系统使用真实的短信API：
- 发送真实短信到用户手机
- 使用阿里云短信服务
- 不在响应中返回验证码

## 配置说明

### 环境变量配置

在 `.env` 文件中配置以下变量：

```env
# 短信服务配置
SMS_ACCESS_KEY_ID=your_aliyun_access_key_id
SMS_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
SMS_SIGN_NAME=your_sms_signature
SMS_TEMPLATE_CODE=your_sms_template_code
```

### 开发环境（当前配置）
```env
# 短信服务配置（开发模式 - 留空即可）
SMS_ACCESS_KEY_ID=
SMS_ACCESS_KEY_SECRET=
SMS_SIGN_NAME=
SMS_TEMPLATE_CODE=
```

### 生产环境配置示例
```env
# 短信服务配置（生产模式）
SMS_ACCESS_KEY_ID=LTAI5tFxxxxxxxxxxxxxxx
SMS_ACCESS_KEY_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
SMS_SIGN_NAME=您的应用名称
SMS_TEMPLATE_CODE=SMS_123456789
```

## API使用

### 发送短信验证码

**请求**
```bash
curl -X POST http://localhost:8080/api/v1/send-sms \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800000001"}'
```

**响应（开发模式）**
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": {
    "code": "333301"
  }
}
```

**响应（生产模式）**
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": {}
}
```

### 验证码验证

验证码会存储在Redis中，有效期5分钟。在用户注册时会自动验证：

```bash
curl -X POST http://localhost:8080/api/v1/register \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800000001",
    "password": "123456",
    "nickname": "测试用户",
    "sms_code": "333301"
  }'
```

## 阿里云短信服务配置

### 1. 开通服务
1. 登录阿里云控制台
2. 开通短信服务
3. 创建签名和模板

### 2. 获取配置信息
- **AccessKey ID** 和 **AccessKey Secret**：在阿里云控制台的访问控制(RAM)中创建
- **签名名称**：在短信服务控制台中创建的签名
- **模板代码**：在短信服务控制台中创建的模板

### 3. 模板格式
推荐的验证码模板格式：
```
您的验证码是${code}，5分钟内有效。
```

## 日志监控

### 开发模式日志
```
[开发模式] 模拟发送短信到 13800000001，验证码: 333301
```

### 生产模式日志
正常情况下不会在日志中显示验证码，只会记录发送结果。

## 安全注意事项

1. **验证码有效期**：5分钟自动过期
2. **验证码存储**：存储在Redis中，验证后自动删除
3. **频率限制**：建议在前端实现发送频率限制
4. **生产环境**：不要在响应中返回验证码

## 故障排除

### 常见错误

1. **AccessKeyId is mandatory**
   - 检查环境变量名称是否正确
   - 确认是否需要配置真实的阿里云参数

2. **签名不匹配**
   - 检查AccessKey Secret是否正确
   - 确认签名算法实现

3. **模板不存在**
   - 检查模板代码是否正确
   - 确认模板已审核通过

### 调试方法

1. 查看服务日志：
   ```bash
   ./run.sh logs
   ```

2. 检查环境配置：
   ```bash
   cat .env | grep SMS
   ```

3. 测试接口：
   ```bash
   curl -X POST http://localhost:8080/api/v1/send-sms \
     -H "Content-Type: application/json" \
     -d '{"phone":"13800000001"}'
   ```

## 总结

- ✅ 短信发送接口已修复并正常工作
- ✅ 支持开发模式和生产模式自动切换
- ✅ 开发环境无需配置真实短信服务
- ✅ 生产环境支持阿里云短信服务
- ✅ 验证码安全存储和验证机制完善
