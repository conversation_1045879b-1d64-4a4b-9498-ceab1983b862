# 前端API接口调整指南

## 🚨 紧急通知：需要前端调整的接口

由于发现了Gin框架路由重定向问题，部分API接口URL需要调整。请前端技术人员立即进行以下修改。

## 📋 需要修改的接口列表

### 1. 应用管理接口（用户端）

| 功能 | 当前URL（错误） | 正确URL | 优先级 |
|------|----------------|---------|--------|
| 获取应用列表 | `GET /api/v1/apps` | `GET /api/v1/apps/` | 🔴 高 |
| 创建应用 | `POST /api/v1/apps` | `POST /api/v1/apps/` | 🔴 高 |

### 2. 管理员接口

| 功能 | 当前URL（错误） | 正确URL | 优先级 |
|------|----------------|---------|--------|
| 获取用户列表 | `GET /api/v1/admin/users` | `GET /api/v1/admin/users/` | 🟡 中 |
| 获取所有应用 | `GET /api/v1/admin/apps` | `GET /api/v1/admin/apps/` | 🟡 中 |
| 获取系统配置 | `GET /api/v1/admin/config` | `GET /api/v1/admin/config/` | 🟡 中 |
| 获取模型列表 | `GET /api/v1/admin/models` | `GET /api/v1/admin/models/` | 🟡 中 |
| 创建模型 | `POST /api/v1/admin/models` | `POST /api/v1/admin/models/` | 🟡 中 |

### 3. 题库管理员接口

| 功能 | 当前URL（错误） | 正确URL | 优先级 |
|------|----------------|---------|--------|
| 获取题目列表 | `GET /api/v1/manager/questions` | `GET /api/v1/manager/questions/` | 🟡 中 |
| 获取日志列表 | `GET /api/v1/manager/logs` | `GET /api/v1/manager/logs/` | 🟡 中 |

## ✅ 不需要修改的接口

以下接口URL正确，无需修改：

### 用户认证接口
- `POST /api/v1/send-sms`
- `POST /api/v1/register`
- `POST /api/v1/login`
- `POST /api/v1/forgot-password`
- `POST /api/v1/reset-password`

### 用户信息接口
- `GET /api/v1/user/profile`
- `PUT /api/v1/user/profile`
- `POST /api/v1/user/change-password`
- `GET /api/v1/user/balance-logs`

### 应用操作接口（带ID的）
- `PUT /api/v1/apps/:id`
- `DELETE /api/v1/apps/:id`
- `POST /api/v1/apps/:id/reset-secret`
- `GET /api/v1/apps/:id/logs`

### 解题接口
- `POST /api/v1/solve/question`

## 🔧 修改示例

### JavaScript/TypeScript 示例

**修改前**：
```javascript
// 错误的URL
const getApps = async () => {
  const response = await fetch('/api/v1/apps', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};

const createApp = async (appData) => {
  const response = await fetch('/api/v1/apps', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(appData)
  });
  return response.json();
};
```

**修改后**：
```javascript
// 正确的URL（注意尾部斜杠）
const getApps = async () => {
  const response = await fetch('/api/v1/apps/', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.json();
};

const createApp = async (appData) => {
  const response = await fetch('/api/v1/apps/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(appData)
  });
  return response.json();
};
```

### Axios 示例

**修改前**：
```javascript
// 错误的URL
const api = axios.create({
  baseURL: '/api/v1'
});

// 获取应用列表
const getApps = () => api.get('/apps');

// 创建应用
const createApp = (data) => api.post('/apps', data);
```

**修改后**：
```javascript
// 正确的URL（注意尾部斜杠）
const api = axios.create({
  baseURL: '/api/v1'
});

// 获取应用列表
const getApps = () => api.get('/apps/');

// 创建应用
const createApp = (data) => api.post('/apps/', data);
```

### React Hook 示例

**修改前**：
```javascript
const useApps = () => {
  const [apps, setApps] = useState([]);
  
  useEffect(() => {
    fetch('/api/v1/apps', {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    .then(res => res.json())
    .then(data => setApps(data.data));
  }, []);
  
  return apps;
};
```

**修改后**：
```javascript
const useApps = () => {
  const [apps, setApps] = useState([]);
  
  useEffect(() => {
    fetch('/api/v1/apps/', {  // 注意尾部斜杠
      headers: { 'Authorization': `Bearer ${token}` }
    })
    .then(res => res.json())
    .then(data => setApps(data.data));
  }, []);
  
  return apps;
};
```

## 🔍 问题原因说明

### 技术原因
- Gin框架的路由组定义：`apps.GET("/", handler)`
- 实际路径为：`/api/v1/apps/`（注意尾部斜杠）
- 访问 `/api/v1/apps` 会返回 301 重定向到 `/api/v1/apps/`

### 影响范围
- 主要影响列表接口（GET）和创建接口（POST）
- 带参数的接口（如 `/apps/:id`）不受影响

## ⚡ 修改优先级

### 🔴 高优先级（立即修改）
1. **应用管理接口**：用户端的核心功能
   - `GET /api/v1/apps/` - 获取应用列表
   - `POST /api/v1/apps/` - 创建应用

### 🟡 中优先级（尽快修改）
2. **管理员接口**：管理后台功能
3. **题库管理员接口**：题库管理功能

### 🟢 低优先级（可延后）
4. 其他可能的列表接口

## 🧪 测试验证

修改完成后，请使用以下方法验证：

### 1. 检查网络请求
- 打开浏览器开发者工具
- 查看Network标签
- 确认请求URL包含尾部斜杠
- 确认返回状态码为200（不是301）

### 2. 功能测试
- 测试应用列表加载
- 测试应用创建功能
- 确认数据正常显示

### 3. 错误处理
- 确认错误信息正常显示
- 验证加载状态正常

## 📞 技术支持

如有疑问，请联系后端开发团队：

### 已验证的正确接口
- ✅ `GET /api/v1/apps/` - 应用列表
- ✅ `POST /api/v1/apps/` - 创建应用
- ✅ `PUT /api/v1/apps/:id` - 更新应用
- ✅ `DELETE /api/v1/apps/:id` - 删除应用

### 测试用例
```bash
# 测试应用列表
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8080/api/v1/apps/

# 测试创建应用
curl -X POST http://localhost:8080/api/v1/apps/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"name": "测试应用"}'
```

## ⏰ 修改时间要求

- **高优先级接口**：请在收到通知后 **2小时内** 完成修改
- **中优先级接口**：请在 **1个工作日内** 完成修改
- **低优先级接口**：请在 **3个工作日内** 完成修改

## 📝 修改确认

请在完成修改后回复确认：
1. 已修改的接口列表
2. 测试验证结果
3. 是否遇到其他问题

**重要提醒**：这个问题会导致应用管理功能完全无法使用，请优先处理高优先级接口！
