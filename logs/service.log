2025/06/17 23:28:38 main.go:37: 📝 [SYSTEM] 日志系统初始化完成，日志将同时输出到终端和文件: logs/service.log
2025/06/17 23:28:38 main.go:46: Warning: .env file not found: unexpected character "-" in variable name near "curl -X GET \"http://localhost:8080/api/v1/manager/logs/?page=1&page_size=20\" \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取单个日志\ncurl -X GET http://localhost:8080/api/v1/manager/logs/{log_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 删除日志\ncurl -X DELETE http://localhost:8080/api/v1/manager/logs/{log_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取日志统计\ncurl -X GET http://localhost:8080/api/v1/manager/logs/stats \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 8. 超级管理员接口 (需要admin权限)\n\n# 获取所有用户列表 (支持过滤)\ncurl -X GET \"http://localhost:8080/api/v1/admin/users/?page=1&page_size=20&role=user&is_active=1\" \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取单个用户详情\ncurl -X GET http://localhost:8080/api/v1/admin/users/{user_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 更新用户信息\ncurl -X PUT http://localhost:8080/api/v1/admin/users/{user_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"nickname\": \"新昵称\",\n    \"is_active\": 1,\n    \"balance\": 5000\n  }'\n\n# 获取用户统计信息\ncurl -X GET http://localhost:8080/api/v1/admin/users/stats \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 应用管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/apps/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 系统配置管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/config/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 模型配置管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/models/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\""

2025/06/17 23:28:38 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[30.565ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:38 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[61.088ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:38 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[61.536ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND table_type = 'BASE TABLE'

2025/06/17 23:28:38 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[30.567ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:38 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[61.740ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:38 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[60.620ms] [34;1m[rows:-][0m SELECT * FROM `hook_user` LIMIT 1

2025/06/17 23:28:38 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[62.126ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_user' ORDER BY ORDINAL_POSITION

2025/06/17 23:28:38 [32m
[0m[33m[30.116ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:38 [32m
[0m[33m[61.619ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:38 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[62.335ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_user' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/17 23:28:38 [32m
[0m[33m[29.865ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:38 [32m
[0m[33m[60.089ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:39 [32m
[0m[33m[59.057ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_user' AND constraint_name = 'uni_hook_user_phone'

2025/06/17 23:28:39 [32m
[0m[33m[29.535ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:39 [32m
[0m[33m[61.212ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:39 [32m
[0m[33m[59.671ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.821ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[60.749ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.624ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.436ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.293ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.821ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND table_type = 'BASE TABLE'

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.555ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.188ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.237ms] [34;1m[rows:-][0m SELECT * FROM `hook_apps` LIMIT 1

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[60.020ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' ORDER BY ORDINAL_POSITION

2025/06/17 23:28:39 [32m
[0m[33m[29.205ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:39 [32m
[0m[33m[59.380ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:39 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[60.366ms] [34;1m[rows:3][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_apps' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/17 23:28:39 [32m
[0m[33m[28.917ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:39 [32m
[0m[33m[60.128ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:39 [32m
[0m[33m[60.434ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_apps' AND constraint_name = 'uni_hook_apps_app_id'

2025/06/17 23:28:40 [32m
[0m[33m[29.300ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:40 [32m
[0m[33m[58.466ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:40 [32m
[0m[33m[58.766ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.063ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.397ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.302ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_user_id'

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.585ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.544ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[57.823ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[28.874ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.889ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.511ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' AND table_type = 'BASE TABLE'

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.015ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.754ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.696ms] [34;1m[rows:-][0m SELECT * FROM `hook_balance_logs` LIMIT 1

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.266ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' ORDER BY ORDINAL_POSITION

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.288ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.265ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.654ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' AND index_name = 'idx_hook_balance_logs_user_id'

2025/06/17 23:28:40 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.322ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.640ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[60.291ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND table_type = 'BASE TABLE'

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.275ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.191ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[57.923ms] [34;1m[rows:-][0m SELECT * FROM `hook_system_config` LIMIT 1

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.338ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' ORDER BY ORDINAL_POSITION

2025/06/17 23:28:41 [32m
[0m[33m[28.849ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:41 [32m
[0m[33m[59.099ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.395ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_system_config' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/17 23:28:41 [32m
[0m[33m[29.006ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:41 [32m
[0m[33m[59.372ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:41 [32m
[0m[33m[58.816ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_system_config' AND constraint_name = 'uni_hook_system_config_key'

2025/06/17 23:28:41 [32m
[0m[33m[29.265ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:41 [32m
[0m[33m[59.741ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:41 [32m
[0m[33m[59.832ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND index_name = 'idx_hook_system_config_key'

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[33.059ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[61.267ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.225ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND index_name = 'idx_hook_system_config_key'

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[31.313ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:41 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.989ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.353ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND table_type = 'BASE TABLE'

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.287ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.946ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.808ms] [34;1m[rows:-][0m SELECT * FROM `hook_models` LIMIT 1

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[60.315ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_models' ORDER BY ORDINAL_POSITION

2025/06/17 23:28:42 [32m
[0m[33m[30.136ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:42 [32m
[0m[33m[59.404ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[60.706ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_models' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/17 23:28:42 [32m
[0m[33m[29.640ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:42 [32m
[0m[33m[58.865ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:42 [32m
[0m[33m[59.447ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_models' AND constraint_name = 'uni_hook_models_model_name'

2025/06/17 23:28:42 [32m
[0m[33m[29.420ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:42 [32m
[0m[33m[57.483ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:42 [32m
[0m[33m[58.696ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND index_name = 'idx_hook_models_model_name'

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[41.161ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `temperature` decimal(3,2) DEFAULT 0.7 COMMENT '温度参数'

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[39.828ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `top_p` decimal(3,2) DEFAULT 0.9 COMMENT 'TopP参数'

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[40.135ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `repetition_penalty` decimal(3,2) DEFAULT 1 COMMENT '重复惩罚'

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[40.033ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `presence_penalty` decimal(3,2) DEFAULT 0 COMMENT '存在惩罚'

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.152ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.530ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:42 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.125ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND index_name = 'idx_hook_models_model_name'

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[28.663ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.736ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.252ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' AND table_type = 'BASE TABLE'

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.005ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.633ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.007ms] [34;1m[rows:-][0m SELECT * FROM `hook_question_bank` LIMIT 1

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.160ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' ORDER BY ORDINAL_POSITION

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[28.632ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.830ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.934ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' AND index_name = 'idx_hook_question_bank_hash_key'

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.693ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.608ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.273ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' AND table_type = 'BASE TABLE'

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.386ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.102ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[58.488ms] [34;1m[rows:-][0m SELECT * FROM `hook_solve_logs` LIMIT 1

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.422ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' ORDER BY ORDINAL_POSITION

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[29.174ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 23:28:43 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.118ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 23:28:44 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:47
[0m[33m[59.123ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' AND index_name = 'idx_hook_solve_logs_app_id'

2025/06/17 23:28:44 [32m/Users/<USER>/Documents/Dev/test/internal/database/mysql.go:67
[0m[33m[59.310ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE role IN ('admin','manager')
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> solve-go-api/internal/router.Setup.func1 (6 handlers)
[GIN-debug] POST   /api/v1/send-sms          --> solve-go-api/internal/handlers.(*AuthHandler).SendSMSCode-fm (6 handlers)
[GIN-debug] POST   /api/v1/register          --> solve-go-api/internal/handlers.(*AuthHandler).Register-fm (6 handlers)
[GIN-debug] POST   /api/v1/login             --> solve-go-api/internal/handlers.(*AuthHandler).Login-fm (6 handlers)
[GIN-debug] POST   /api/v1/forgot-password   --> solve-go-api/internal/handlers.(*AuthHandler).ForgotPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/reset-password    --> solve-go-api/internal/handlers.(*AuthHandler).ResetPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/solve/question    --> solve-go-api/internal/handlers.(*SolveHandler).SolveQuestion-fm (7 handlers)
[GIN-debug] GET    /api/v1/user/profile      --> solve-go-api/internal/handlers.(*AuthHandler).GetProfile-fm (7 handlers)
[GIN-debug] PUT    /api/v1/user/profile      --> solve-go-api/internal/handlers.(*AuthHandler).UpdateProfile-fm (7 handlers)
[GIN-debug] POST   /api/v1/user/change-password --> solve-go-api/internal/handlers.(*AuthHandler).ChangePassword-fm (7 handlers)
[GIN-debug] GET    /api/v1/user/balance-logs --> solve-go-api/internal/handlers.(*AuthHandler).GetBalanceLogs-fm (7 handlers)
[GIN-debug] GET    /api/v1/apps/             --> solve-go-api/internal/handlers.(*AppHandler).GetApps-fm (7 handlers)
[GIN-debug] POST   /api/v1/apps/             --> solve-go-api/internal/handlers.(*AppHandler).CreateApp-fm (7 handlers)
[GIN-debug] PUT    /api/v1/apps/:id          --> solve-go-api/internal/handlers.(*AppHandler).UpdateApp-fm (7 handlers)
[GIN-debug] DELETE /api/v1/apps/:id          --> solve-go-api/internal/handlers.(*AppHandler).DeleteApp-fm (7 handlers)
[GIN-debug] POST   /api/v1/apps/:id/reset-secret --> solve-go-api/internal/handlers.(*AppHandler).ResetSecret-fm (7 handlers)
[GIN-debug] GET    /api/v1/apps/:id/logs     --> solve-go-api/internal/handlers.(*AppHandler).GetAppLogs-fm (7 handlers)
[GIN-debug] GET    /api/v1/manager/questions/ --> solve-go-api/internal/handlers.(*QuestionHandler).GetQuestions-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).GetQuestion-fm (8 handlers)
[GIN-debug] PUT    /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).UpdateQuestion-fm (8 handlers)
[GIN-debug] DELETE /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).DeleteQuestion-fm (8 handlers)
[GIN-debug] POST   /api/v1/manager/questions/:id/verify --> solve-go-api/internal/handlers.(*QuestionHandler).VerifyQuestion-fm (8 handlers)
[GIN-debug] POST   /api/v1/manager/questions/:id/unverify --> solve-go-api/internal/handlers.(*QuestionHandler).UnverifyQuestion-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/questions/stats --> solve-go-api/internal/handlers.(*QuestionHandler).GetStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/     --> solve-go-api/internal/handlers.(*LogHandler).GetLogs-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/:id  --> solve-go-api/internal/handlers.(*LogHandler).GetLog-fm (8 handlers)
[GIN-debug] DELETE /api/v1/manager/logs/:id  --> solve-go-api/internal/handlers.(*LogHandler).DeleteLog-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/stats --> solve-go-api/internal/handlers.(*LogHandler).GetStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/      --> solve-go-api/internal/handlers.(*AdminHandler).GetUsers-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/:id   --> solve-go-api/internal/handlers.(*AdminHandler).GetUser-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/users/:id   --> solve-go-api/internal/handlers.(*AdminHandler).UpdateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/activate --> solve-go-api/internal/handlers.(*AdminHandler).ActivateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/deactivate --> solve-go-api/internal/handlers.(*AdminHandler).DeactivateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/recharge --> solve-go-api/internal/handlers.(*AdminHandler).RechargeUser-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/stats --> solve-go-api/internal/handlers.(*AdminHandler).GetUserStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/pending --> solve-go-api/internal/handlers.(*AdminHandler).GetPendingUsers-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/approve --> solve-go-api/internal/handlers.(*AdminHandler).ApproveUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/reject --> solve-go-api/internal/handlers.(*AdminHandler).RejectUser-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/apps/       --> solve-go-api/internal/handlers.(*AdminHandler).GetAllApps-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/apps/:id    --> solve-go-api/internal/handlers.(*AdminHandler).UpdateApp-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/apps/:id/activate --> solve-go-api/internal/handlers.(*AdminHandler).ActivateApp-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/apps/:id/deactivate --> solve-go-api/internal/handlers.(*AdminHandler).DeactivateApp-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/config/     --> solve-go-api/internal/handlers.(*AdminHandler).GetConfigs-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/config/:key --> solve-go-api/internal/handlers.(*AdminHandler).UpdateConfig-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/models/     --> solve-go-api/internal/handlers.(*AdminHandler).GetModels-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/models/     --> solve-go-api/internal/handlers.(*AdminHandler).CreateModel-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/models/:id  --> solve-go-api/internal/handlers.(*AdminHandler).UpdateModel-fm (8 handlers)
[GIN-debug] DELETE /api/v1/admin/models/:id  --> solve-go-api/internal/handlers.(*AdminHandler).DeleteModel-fm (8 handlers)
2025/06/17 23:28:44 main.go:92: Server starting on port 8080
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8080

2025/06/17 23:28:48 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:95
[0m[33m[58.725ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' AND app_secret = 'VsubbUlgZejHgRrylJ0qs3A9HJy3a8P2' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 23:28:48 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:111
[0m[33m[59.006ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 23:28:48 solve_handler.go:31: 🎯 [HANDLER] 收到解题请求 - IP: ::1, UserAgent: PostmanRuntime-ApipostRuntime/1.1.0
2025/06/17 23:28:48 solve_handler.go:63: ✅ [HANDLER] 请求参数解析成功 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:28:48 solve_handler.go:66: 🚀 [HANDLER] 开始处理解题请求
2025/06/17 23:28:48 solve_service.go:62: 🚀 [SOLVE] 开始处理解题请求 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:28:48 solve_service.go:65: 📋 [SOLVE] 步骤1: 验证应用和用户

2025/06/17 23:28:48 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:53
[0m[33m[60.736ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 23:28:48 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:111
[0m[33m[59.257ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 23:28:48 solve_service.go:71: ✅ [SOLVE] 应用/用户验证成功 - UserID: 20, Balance: 500000
2025/06/17 23:28:48 solve_service.go:74: 💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: 500000
2025/06/17 23:28:48 solve_service.go:82: 🔍 [SOLVE] 步骤3: 调用OCR模型识别图片
2025/06/17 23:28:48 solve_service.go:175: 🔍 [OCR] 开始OCR处理 - 模型: qwen-vl-plus, 图片URL: http://img.igmdns.com/images/cc0001.jpg

2025/06/17 23:28:49 [32m/Users/<USER>/Documents/Dev/test/internal/services/model_service.go:39
[0m[33m[59.512ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-vl-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 23:28:49 model_service.go:215: 🔍 [HTTP-REQUEST] 模型: qwen-vl-plus
2025/06/17 23:28:49 model_service.go:216: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation
2025/06/17 23:28:49 model_service.go:217: 🔍 [HTTP-REQUEST] 请求体长度: 573 bytes
2025/06/17 23:28:49 model_service.go:222: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}",
        "role": "system"
      },
      {
        "content": [
          {
            "image": "http://img.igmdns.com/images/cc0001.jpg"
          },
          {
            "text": "精准且完整的识别问题,严格按照要求输出的完整json,其中的options字段的格式必须正确,选项字母为键，选项内容为值"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.2,
    "top_k": 1,
    "top_p": 0.01
  }
}
2025/06/17 23:28:49 model_service.go:241: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/17 23:28:49 model_service.go:242: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/17 23:28:50 model_service.go:263: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/17 23:28:50 model_service.go:264: 🔍 [HTTP-RESPONSE] 响应体长度: 755 bytes
2025/06/17 23:28:50 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/17 23:28:50 model_service.go:270: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "choices": [
      {
        "finish_reason": "stop",
        "message": {
          "role": "assistant",
          "content": [
            {
              "text": "```json\n{\n    \"qutext\": \"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\",\n    \"options\": {\n        \"A\": \"左侧A柱盲区内可能有行人将要通过\",\n        \"B\": \"对向车道车辆将要调头\",\n        \"C\": \"后面有车辆将超车\",\n        \"D\": \"右侧车道有车辆将要通过\"\n    }\n}\n```"
            }
          ]
        }
      }
    ]
  },
  "usage": {
    "input_tokens_details": {
      "text_tokens": 72,
      "image_tokens": 860
    },
    "total_tokens": 1030,
    "output_tokens": 98,
    "input_tokens": 932,
    "output_tokens_details": {
      "text_tokens": 98
    },
    "image_tokens": 860,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "6d20f9dd-aecd-93e4-98a1-835b2d68fe34"
}
2025/06/17 23:28:50 model_service.go:282: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/17 23:28:50 solve_service.go:183: ✅ [OCR] 模型调用成功 - Token消耗: 1030
2025/06/17 23:28:50 solve_service.go:189: 📝 [OCR] 开始解析OCR响应
2025/06/17 23:28:50 parser_service.go:30: 🔍 [OCR-DEBUG] Content类型: []interface {}
2025/06/17 23:28:50 parser_service.go:39: 🔍 [OCR-DEBUG] Array类型，元素数量: 1
2025/06/17 23:28:50 parser_service.go:42: 🔍 [OCR-DEBUG] Array[0]类型: map[string]interface {}
2025/06/17 23:28:50 parser_service.go:47: 🔍 [OCR-DEBUG] 提取文本: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:28:50 parser_service.go:59: 🔍 [OCR-DEBUG] 最终内容长度: 332
2025/06/17 23:28:50 parser_service.go:60: 🔍 [OCR-DEBUG] 最终内容前200字符: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:28:50 parser_service.go:62: 🔍 [OCR-DEBUG] 最终内容后200字符: : "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:28:50 parser_service.go:279: 🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:28:50 parser_service.go:290: 🔍 [JSON-EXTRACT] 从```json```中提取: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:28:50 parser_service.go:320: 🔍 [FIELD-MAP] 开始字段映射，原始JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:28:50 parser_service.go:325: 🔍 [FIELD-MAP] 映射完成，结果: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:28:50 parser_service.go:70: 🔍 [OCR-DEBUG] 字段映射后的JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:28:50 parser_service.go:80: ✅ [OCR-PARSE] JSON解析成功
2025/06/17 23:28:50 parser_service.go:81: 📋 [OCR-PARSE] 解析结果 - QuText: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:28:50 parser_service.go:82: 📋 [OCR-PARSE] 解析结果 - Options数量: 4
2025/06/17 23:28:50 parser_service.go:84: 📋 [OCR-PARSE] 选项 A: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:28:50 parser_service.go:84: 📋 [OCR-PARSE] 选项 B: 对向车道车辆将要调头
2025/06/17 23:28:50 parser_service.go:84: 📋 [OCR-PARSE] 选项 C: 后面有车辆将超车
2025/06/17 23:28:50 parser_service.go:84: 📋 [OCR-PARSE] 选项 D: 右侧车道有车辆将要通过
2025/06/17 23:28:50 parser_service.go:88: 🔍 [TYPE-DETECT] 开始检测题目类型，题干内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:28:50 parser_service.go:160: 🔍 [TYPE-DETECT] 原始内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:28:50 parser_service.go:162: 🔍 [TYPE-DETECT] 转换小写后: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:28:50 parser_service.go:165: 🔍 [TYPE-DETECT] 检测到单选题
2025/06/17 23:28:50 parser_service.go:90: 🔍 [TYPE-DETECT] 检测结果: 单选题
2025/06/17 23:28:50 parser_service.go:97: 🧹 [DATA-CLEAN] 开始清洗题干内容（二次提纯）
2025/06/17 23:28:50 parser_service.go:98: 🧹 [DATA-CLEAN] 原始题干: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:28:50 parser_service.go:190: 🧹 [DATA-CLEAN] 第一步清除前缀后: 如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:28:50 parser_service.go:195: 🧹 [DATA-CLEAN] 第二步清理标点后: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:28:50 parser_service.go:100: 🧹 [DATA-CLEAN] 二次提纯完成，最终题干: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:28:50 parser_service.go:103: 🧹 [DATA-CLEAN] 开始清洗选项内容
2025/06/17 23:28:50 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 A 清理标点前: 左侧A柱盲区内可能有行人将要通过, 清理后: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:28:50 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 B 清理标点前: 对向车道车辆将要调头, 清理后: 对向车道车辆将要调头
2025/06/17 23:28:50 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 C 清理标点前: 后面有车辆将超车, 清理后: 后面有车辆将超车
2025/06/17 23:28:50 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 D 清理标点前: 右侧车道有车辆将要通过, 清理后: 右侧车道有车辆将要通过
2025/06/17 23:28:50 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 C: 后面有车辆将超车
2025/06/17 23:28:50 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 D: 右侧车道有车辆将要通过
2025/06/17 23:28:50 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 A: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:28:50 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 B: 对向车道车辆将要调头
2025/06/17 23:28:50 parser_service.go:111: 📏 [DATA-CALC] 题干字符长度: 18
2025/06/17 23:28:50 parser_service.go:115: 🔑 [DATA-CALC] 生成哈希键: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:28:50 parser_service.go:129: 📦 [SAVE-QUESTION] 构建完成
2025/06/17 23:28:50 parser_service.go:130: 📦 [SAVE-QUESTION] Type: 单选题
2025/06/17 23:28:50 parser_service.go:131: 📦 [SAVE-QUESTION] Content: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:28:50 parser_service.go:132: 📦 [SAVE-QUESTION] CleanContent: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:28:50 parser_service.go:133: 📦 [SAVE-QUESTION] UserURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:28:50 parser_service.go:134: 📦 [SAVE-QUESTION] QuestionLen: 18
2025/06/17 23:28:50 parser_service.go:135: 📦 [SAVE-QUESTION] HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:28:50 parser_service.go:136: 📦 [SAVE-QUESTION] Options数量: 4
2025/06/17 23:28:50 solve_service.go:195: ✅ [OCR] 响应解析成功 - 题目类型: 单选题, 内容长度: 76
2025/06/17 23:28:50 solve_service.go:199: 🔄 [CONTEXT] ProcessContext已更新
2025/06/17 23:28:50 solve_service.go:200: 🔄 [CONTEXT] SaveQuestion.Type: 单选题
2025/06/17 23:28:50 solve_service.go:201: 🔄 [CONTEXT] SaveQuestion.HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:28:50 solve_service.go:202: 🔄 [CONTEXT] SaveQuestion.QuestionLen: 18
2025/06/17 23:28:50 solve_service.go:203: 🔄 [CONTEXT] SaveQuestion.UserURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:28:50 solve_service.go:204: 🔄 [CONTEXT] OCRToken: 1030
2025/06/17 23:28:50 solve_service.go:205: 🔄 [CONTEXT] 当前处理阶段: OCR解析完成，准备进入缓存检查阶段
2025/06/17 23:28:50 solve_service.go:88: ✅ [SOLVE] OCR处理成功 - Token消耗: 1030, 题目类型: 单选题
2025/06/17 23:28:50 solve_service.go:91: 🔄 [SOLVE] 步骤4: 检查Redis缓存
2025/06/17 23:28:50 solve_service.go:212: 🔍 [CACHE-CHECK] 开始检查Redis缓存
2025/06/17 23:28:50 solve_service.go:213: 🔍 [CACHE-CHECK] 使用HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:28:50 solve_service.go:217: ⚪ [CACHE-CHECK] Redis缓存未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:28:50 solve_service.go:218: 🔄 [CONTEXT] 缓存检查结果: 未命中，继续下一步
2025/06/17 23:28:50 solve_service.go:96: ⚪ [SOLVE] Redis缓存未命中
2025/06/17 23:28:50 solve_service.go:99: 🎯 [SOLVE] 步骤5: 检查MySQL精确匹配
2025/06/17 23:28:50 solve_service.go:240: 🔍 [MYSQL-EXACT] 开始MySQL精确匹配
2025/06/17 23:28:50 solve_service.go:241: 🔍 [MYSQL-EXACT] 使用HashKey: 06b2720385c5e4c0df2405cbcbf815ab

2025/06/17 23:28:50 [32m/Users/<USER>/Documents/Dev/test/internal/services/question_service.go:23
[0m[33m[59.587ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE hash_key = '06b2720385c5e4c0df2405cbcbf815ab'
2025/06/17 23:28:50 solve_service.go:245: ⚪ [MYSQL-EXACT] MySQL精确匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:28:50 solve_service.go:246: 🔄 [CONTEXT] 精确匹配结果: 未命中，继续模糊匹配
2025/06/17 23:28:50 solve_service.go:104: ⚪ [SOLVE] MySQL精确匹配未命中
2025/06/17 23:28:50 solve_service.go:107: 🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配
2025/06/17 23:28:50 solve_service.go:278: 🔍 [MYSQL-FUZZY] 开始MySQL模糊匹配
2025/06/17 23:28:50 solve_service.go:279: 🔍 [MYSQL-FUZZY] 题目类型: 单选题
2025/06/17 23:28:50 solve_service.go:280: 🔍 [MYSQL-FUZZY] 清洗后内容: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:28:50 solve_service.go:281: 🔍 [MYSQL-FUZZY] 题目长度: 18
2025/06/17 23:28:50 match_service.go:30: 🔍 [FUZZY-MATCH] 开始三步筛选匹配

2025/06/17 23:28:51 [32m/Users/<USER>/Documents/Dev/test/internal/services/match_service.go:41
[0m[33m[58.286ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE verified = true AND type = 2 AND ABS(question_len - 18) <= 5
2025/06/17 23:28:51 match_service.go:47: 🔍 [STEP-1] MySQL筛选完成 - 找到0个候选题目
2025/06/17 23:28:51 solve_service.go:285: ⚪ [MYSQL-FUZZY] MySQL模糊匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:28:51 solve_service.go:286: 🔄 [CONTEXT] 模糊匹配结果: 未命中，需要调用AI模型
2025/06/17 23:28:51 solve_service.go:112: ⚪ [SOLVE] MySQL模糊匹配未命中
2025/06/17 23:28:51 solve_service.go:115: 🤖 [SOLVE] 步骤7: 调用Solve模型解答
2025/06/17 23:28:51 solve_service.go:374: 📦 [SAVE-QUESTION] CleanContent: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:28:51 solve_service.go:396: ✅ [LOG-FILE] CleanContent已记录到 logs/clean_content.log

2025/06/17 23:28:51 [32m/Users/<USER>/Documents/Dev/test/internal/services/model_service.go:39
[0m[33m[60.212ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 23:28:51 model_service.go:215: 🔍 [HTTP-REQUEST] 模型: qwen-plus
2025/06/17 23:28:51 model_service.go:216: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025/06/17 23:28:51 model_service.go:217: 🔍 [HTTP-REQUEST] 请求体长度: 719 bytes
2025/06/17 23:28:51 model_service.go:222: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "你是一个专业的题目解答助手，请根据题目内容给出正确答案和详细解析。",
        "role": "system"
      },
      {
        "content": "请解答这道题目，返回JSON格式：{\"answer\":\"答案\",\"analysis\":\"解析内容\"}\n\n题目内容：题目类型：单选题\n题干：(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\n选项：map[A:左侧A柱盲区内可能有行人将要通过 B:对向车道车辆将要调头 C:后面有车辆将超车 D:右侧车道有车辆将要通过]",
        "role": "user"
      }
    ]
  },
  "model": "qwen-plus",
  "parameters": {
    "presence_penalty": 0,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.3,
    "top_k": 50,
    "top_p": 0.9
  }
}
2025/06/17 23:28:51 model_service.go:241: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/17 23:28:51 model_service.go:242: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/17 23:28:54 model_service.go:263: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/17 23:28:54 model_service.go:264: 🔍 [HTTP-RESPONSE] 响应体长度: 768 bytes
2025/06/17 23:28:54 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/17 23:28:54 model_service.go:270: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "finish_reason": "stop",
    "text": "```json\n{\n  \"answer\": \"A\",\n  \"analysis\": \"解析内容：在驾驶过程中，当遇到题目中描述的情况时，驾驶员需要注意左侧A柱盲区。A柱是汽车前挡风玻璃两侧的支柱，可能会遮挡驾驶员的视线，导致盲区内的情况无法直接观察到。因此，选项A提到的‘左侧A柱盲区内可能有行人将要通过’是正确的注意事项。其他选项中，B、C、D虽然描述了可能的交通情况，但与题目中强调的A柱盲区无直接关联，因此不作为正确答案。\"\n}\n```"
  },
  "usage": {
    "total_tokens": 258,
    "output_tokens": 130,
    "input_tokens": 128,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "7252c20c-f29c-97ff-9fc2-41c9d9b08926"
}
2025/06/17 23:28:54 model_service.go:282: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/17 23:28:54 model_service.go:286: 🔍 [HTTP-DEBUG] Choices为空，尝试处理Solve模型格式
2025/06/17 23:28:54 model_service.go:293: 🔍 [HTTP-DEBUG] 从output.text提取内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：在驾驶过程中，当遇到题目中描�
2025/06/17 23:28:54 model_service.go:309: 🔍 [HTTP-DEBUG] 成功转换为标准格式
2025/06/17 23:28:54 model_service.go:160: 🔍 [SOLVE-DEBUG] 原始响应Choices数量: 1
2025/06/17 23:28:54 model_service.go:161: 🔍 [SOLVE-DEBUG] 原始响应Usage: {TotalTokens:258}
2025/06/17 23:28:54 model_service.go:163: 🔍 [SOLVE-DEBUG] 第一个Choice内容类型: string
2025/06/17 23:28:54 model_service.go:164: 🔍 [SOLVE-DEBUG] 第一个Choice内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：在驾驶过程中，当遇到题目中描述的情况时，驾驶员需要注意左侧A柱盲区。A柱是汽车前挡风玻璃两侧的支柱，可能会遮挡驾驶员的视线，导致盲区内的情况无法直接观察到。因此，选项A提到的‘左侧A柱盲区内可能有行人将要通过’是正确的注意事项。其他选项中，B、C、D虽然描述了可能的交通情况，但与题目中强调的A柱盲区无直接关联，因此不作为正确答案。"
}
```
2025/06/17 23:28:54 solve_service.go:121: ❌ [SOLVE] Solve模型处理失败: failed to parse solve response JSON: invalid character '`' looking for beginning of value
2025/06/17 23:28:54 solve_handler.go:74: 📝 [HANDLER] 记录解题日志

2025/06/17 23:28:55 [32m/Users/<USER>/Documents/Dev/test/internal/services/log_service.go:41
[0m[33m[120.325ms] [34;1m[rows:1][0m INSERT INTO `hook_solve_logs` (`app_id`,`user_url`,`matched_id`,`ocr_token`,`source`,`data`,`status`,`latency`,`created_at`) VALUES ('NdOCNqqWPtrLFvZj','http://img.igmdns.com/images/cc0001.jpg',0,1030,'',(NULL),0,6170,'2025-06-17 23:28:55.026')
2025/06/17 23:28:55 solve_handler.go:115: ❌ [HANDLER] 解题处理失败 - Status: 0, Message: failed to parse solve response JSON: invalid character '`' looking for beginning of value
time="2025-06-17T23:28:55+08:00" level=info msg="HTTP Request" body_size=114 client_ip="::1" error= latency=6.409954958s method=POST path=/api/v1/solve/question status_code=200 timestamp="2025-06-17T23:28:55+08:00" user_agent=PostmanRuntime-ApipostRuntime/1.1.0
[GIN] 2025/06/17 - 23:28:55 | 200 |  6.409994208s |             ::1 | POST     "/api/v1/solve/question"

2025/06/17 23:29:40 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:95
[0m[33m[62.920ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' AND app_secret = 'VsubbUlgZejHgRrylJ0qs3A9HJy3a8P2' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 23:29:40 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:111
[0m[33m[59.458ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 23:29:40 solve_handler.go:31: 🎯 [HANDLER] 收到解题请求 - IP: ::1, UserAgent: PostmanRuntime-ApipostRuntime/1.1.0
2025/06/17 23:29:40 solve_handler.go:63: ✅ [HANDLER] 请求参数解析成功 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:29:40 solve_handler.go:66: 🚀 [HANDLER] 开始处理解题请求
2025/06/17 23:29:40 solve_service.go:62: 🚀 [SOLVE] 开始处理解题请求 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:29:40 solve_service.go:65: 📋 [SOLVE] 步骤1: 验证应用和用户

2025/06/17 23:29:40 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:53
[0m[33m[59.360ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 23:29:40 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:111
[0m[33m[59.180ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 23:29:40 solve_service.go:71: ✅ [SOLVE] 应用/用户验证成功 - UserID: 20, Balance: 500000
2025/06/17 23:29:40 solve_service.go:74: 💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: 500000
2025/06/17 23:29:40 solve_service.go:82: 🔍 [SOLVE] 步骤3: 调用OCR模型识别图片
2025/06/17 23:29:40 solve_service.go:175: 🔍 [OCR] 开始OCR处理 - 模型: qwen-vl-plus, 图片URL: http://img.igmdns.com/images/cc0001.jpg

2025/06/17 23:29:41 [32m/Users/<USER>/Documents/Dev/test/internal/services/model_service.go:39
[0m[33m[59.667ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-vl-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 23:29:41 model_service.go:215: 🔍 [HTTP-REQUEST] 模型: qwen-vl-plus
2025/06/17 23:29:41 model_service.go:216: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation
2025/06/17 23:29:41 model_service.go:217: 🔍 [HTTP-REQUEST] 请求体长度: 573 bytes
2025/06/17 23:29:41 model_service.go:222: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}",
        "role": "system"
      },
      {
        "content": [
          {
            "image": "http://img.igmdns.com/images/cc0001.jpg"
          },
          {
            "text": "精准且完整的识别问题,严格按照要求输出的完整json,其中的options字段的格式必须正确,选项字母为键，选项内容为值"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.2,
    "top_k": 1,
    "top_p": 0.01
  }
}
2025/06/17 23:29:41 model_service.go:241: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/17 23:29:41 model_service.go:242: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/17 23:29:43 model_service.go:263: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/17 23:29:43 model_service.go:264: 🔍 [HTTP-RESPONSE] 响应体长度: 755 bytes
2025/06/17 23:29:43 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/17 23:29:43 model_service.go:270: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "choices": [
      {
        "finish_reason": "stop",
        "message": {
          "role": "assistant",
          "content": [
            {
              "text": "```json\n{\n    \"qutext\": \"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\",\n    \"options\": {\n        \"A\": \"左侧A柱盲区内可能有行人将要通过\",\n        \"B\": \"对向车道车辆将要调头\",\n        \"C\": \"后面有车辆将超车\",\n        \"D\": \"右侧车道有车辆将要通过\"\n    }\n}\n```"
            }
          ]
        }
      }
    ]
  },
  "usage": {
    "input_tokens_details": {
      "text_tokens": 72,
      "image_tokens": 860
    },
    "total_tokens": 1030,
    "output_tokens": 98,
    "input_tokens": 932,
    "output_tokens_details": {
      "text_tokens": 98
    },
    "image_tokens": 860,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "adb8a50a-7a06-9617-9eb2-af2256a58bd9"
}
2025/06/17 23:29:43 model_service.go:282: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/17 23:29:43 solve_service.go:183: ✅ [OCR] 模型调用成功 - Token消耗: 1030
2025/06/17 23:29:43 solve_service.go:189: 📝 [OCR] 开始解析OCR响应
2025/06/17 23:29:43 parser_service.go:30: 🔍 [OCR-DEBUG] Content类型: []interface {}
2025/06/17 23:29:43 parser_service.go:39: 🔍 [OCR-DEBUG] Array类型，元素数量: 1
2025/06/17 23:29:43 parser_service.go:42: 🔍 [OCR-DEBUG] Array[0]类型: map[string]interface {}
2025/06/17 23:29:43 parser_service.go:47: 🔍 [OCR-DEBUG] 提取文本: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:29:43 parser_service.go:59: 🔍 [OCR-DEBUG] 最终内容长度: 332
2025/06/17 23:29:43 parser_service.go:60: 🔍 [OCR-DEBUG] 最终内容前200字符: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:29:43 parser_service.go:62: 🔍 [OCR-DEBUG] 最终内容后200字符: : "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:29:43 parser_service.go:279: 🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:29:43 parser_service.go:290: 🔍 [JSON-EXTRACT] 从```json```中提取: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:29:43 parser_service.go:320: 🔍 [FIELD-MAP] 开始字段映射，原始JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:29:43 parser_service.go:325: 🔍 [FIELD-MAP] 映射完成，结果: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:29:43 parser_service.go:70: 🔍 [OCR-DEBUG] 字段映射后的JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:29:43 parser_service.go:80: ✅ [OCR-PARSE] JSON解析成功
2025/06/17 23:29:43 parser_service.go:81: 📋 [OCR-PARSE] 解析结果 - QuText: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:29:43 parser_service.go:82: 📋 [OCR-PARSE] 解析结果 - Options数量: 4
2025/06/17 23:29:43 parser_service.go:84: 📋 [OCR-PARSE] 选项 A: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:29:43 parser_service.go:84: 📋 [OCR-PARSE] 选项 B: 对向车道车辆将要调头
2025/06/17 23:29:43 parser_service.go:84: 📋 [OCR-PARSE] 选项 C: 后面有车辆将超车
2025/06/17 23:29:43 parser_service.go:84: 📋 [OCR-PARSE] 选项 D: 右侧车道有车辆将要通过
2025/06/17 23:29:43 parser_service.go:88: 🔍 [TYPE-DETECT] 开始检测题目类型，题干内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:29:43 parser_service.go:160: 🔍 [TYPE-DETECT] 原始内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:29:43 parser_service.go:162: 🔍 [TYPE-DETECT] 转换小写后: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:29:43 parser_service.go:165: 🔍 [TYPE-DETECT] 检测到单选题
2025/06/17 23:29:43 parser_service.go:90: 🔍 [TYPE-DETECT] 检测结果: 单选题
2025/06/17 23:29:43 parser_service.go:97: 🧹 [DATA-CLEAN] 开始清洗题干内容（二次提纯）
2025/06/17 23:29:43 parser_service.go:98: 🧹 [DATA-CLEAN] 原始题干: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:29:43 parser_service.go:190: 🧹 [DATA-CLEAN] 第一步清除前缀后: 如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:29:43 parser_service.go:195: 🧹 [DATA-CLEAN] 第二步清理标点后: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:29:43 parser_service.go:100: 🧹 [DATA-CLEAN] 二次提纯完成，最终题干: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:29:43 parser_service.go:103: 🧹 [DATA-CLEAN] 开始清洗选项内容
2025/06/17 23:29:43 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 C 清理标点前: 后面有车辆将超车, 清理后: 后面有车辆将超车
2025/06/17 23:29:43 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 D 清理标点前: 右侧车道有车辆将要通过, 清理后: 右侧车道有车辆将要通过
2025/06/17 23:29:43 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 A 清理标点前: 左侧A柱盲区内可能有行人将要通过, 清理后: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:29:43 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 B 清理标点前: 对向车道车辆将要调头, 清理后: 对向车道车辆将要调头
2025/06/17 23:29:43 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 D: 右侧车道有车辆将要通过
2025/06/17 23:29:43 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 A: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:29:43 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 B: 对向车道车辆将要调头
2025/06/17 23:29:43 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 C: 后面有车辆将超车
2025/06/17 23:29:43 parser_service.go:111: 📏 [DATA-CALC] 题干字符长度: 18
2025/06/17 23:29:43 parser_service.go:115: 🔑 [DATA-CALC] 生成哈希键: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:29:43 parser_service.go:129: 📦 [SAVE-QUESTION] 构建完成
2025/06/17 23:29:43 parser_service.go:130: 📦 [SAVE-QUESTION] Type: 单选题
2025/06/17 23:29:43 parser_service.go:131: 📦 [SAVE-QUESTION] Content: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:29:43 parser_service.go:132: 📦 [SAVE-QUESTION] CleanContent: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:29:43 parser_service.go:133: 📦 [SAVE-QUESTION] UserURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:29:43 parser_service.go:134: 📦 [SAVE-QUESTION] QuestionLen: 18
2025/06/17 23:29:43 parser_service.go:135: 📦 [SAVE-QUESTION] HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:29:43 parser_service.go:136: 📦 [SAVE-QUESTION] Options数量: 4
2025/06/17 23:29:43 solve_service.go:195: ✅ [OCR] 响应解析成功 - 题目类型: 单选题, 内容长度: 76
2025/06/17 23:29:43 solve_service.go:199: 🔄 [CONTEXT] ProcessContext已更新
2025/06/17 23:29:43 solve_service.go:200: 🔄 [CONTEXT] SaveQuestion.Type: 单选题
2025/06/17 23:29:43 solve_service.go:201: 🔄 [CONTEXT] SaveQuestion.HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:29:43 solve_service.go:202: 🔄 [CONTEXT] SaveQuestion.QuestionLen: 18
2025/06/17 23:29:43 solve_service.go:203: 🔄 [CONTEXT] SaveQuestion.UserURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:29:43 solve_service.go:204: 🔄 [CONTEXT] OCRToken: 1030
2025/06/17 23:29:43 solve_service.go:205: 🔄 [CONTEXT] 当前处理阶段: OCR解析完成，准备进入缓存检查阶段
2025/06/17 23:29:43 solve_service.go:88: ✅ [SOLVE] OCR处理成功 - Token消耗: 1030, 题目类型: 单选题
2025/06/17 23:29:43 solve_service.go:91: 🔄 [SOLVE] 步骤4: 检查Redis缓存
2025/06/17 23:29:43 solve_service.go:212: 🔍 [CACHE-CHECK] 开始检查Redis缓存
2025/06/17 23:29:43 solve_service.go:213: 🔍 [CACHE-CHECK] 使用HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:29:43 solve_service.go:217: ⚪ [CACHE-CHECK] Redis缓存未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:29:43 solve_service.go:218: 🔄 [CONTEXT] 缓存检查结果: 未命中，继续下一步
2025/06/17 23:29:43 solve_service.go:96: ⚪ [SOLVE] Redis缓存未命中
2025/06/17 23:29:43 solve_service.go:99: 🎯 [SOLVE] 步骤5: 检查MySQL精确匹配
2025/06/17 23:29:43 solve_service.go:240: 🔍 [MYSQL-EXACT] 开始MySQL精确匹配
2025/06/17 23:29:43 solve_service.go:241: 🔍 [MYSQL-EXACT] 使用HashKey: 06b2720385c5e4c0df2405cbcbf815ab

2025/06/17 23:29:43 [32m/Users/<USER>/Documents/Dev/test/internal/services/question_service.go:23
[0m[33m[59.566ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE hash_key = '06b2720385c5e4c0df2405cbcbf815ab'
2025/06/17 23:29:43 solve_service.go:245: ⚪ [MYSQL-EXACT] MySQL精确匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:29:43 solve_service.go:246: 🔄 [CONTEXT] 精确匹配结果: 未命中，继续模糊匹配
2025/06/17 23:29:43 solve_service.go:104: ⚪ [SOLVE] MySQL精确匹配未命中
2025/06/17 23:29:43 solve_service.go:107: 🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配
2025/06/17 23:29:43 solve_service.go:278: 🔍 [MYSQL-FUZZY] 开始MySQL模糊匹配
2025/06/17 23:29:43 solve_service.go:279: 🔍 [MYSQL-FUZZY] 题目类型: 单选题
2025/06/17 23:29:43 solve_service.go:280: 🔍 [MYSQL-FUZZY] 清洗后内容: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:29:43 solve_service.go:281: 🔍 [MYSQL-FUZZY] 题目长度: 18
2025/06/17 23:29:43 match_service.go:30: 🔍 [FUZZY-MATCH] 开始三步筛选匹配

2025/06/17 23:29:43 [32m/Users/<USER>/Documents/Dev/test/internal/services/match_service.go:41
[0m[33m[59.413ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE verified = true AND type = 2 AND ABS(question_len - 18) <= 5
2025/06/17 23:29:43 match_service.go:47: 🔍 [STEP-1] MySQL筛选完成 - 找到0个候选题目
2025/06/17 23:29:43 solve_service.go:285: ⚪ [MYSQL-FUZZY] MySQL模糊匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:29:43 solve_service.go:286: 🔄 [CONTEXT] 模糊匹配结果: 未命中，需要调用AI模型
2025/06/17 23:29:43 solve_service.go:112: ⚪ [SOLVE] MySQL模糊匹配未命中
2025/06/17 23:29:43 solve_service.go:115: 🤖 [SOLVE] 步骤7: 调用Solve模型解答
2025/06/17 23:29:43 solve_service.go:374: 📦 [SAVE-QUESTION] CleanContent: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:29:43 solve_service.go:396: ✅ [LOG-FILE] CleanContent已记录到 logs/clean_content.log

2025/06/17 23:29:43 [32m/Users/<USER>/Documents/Dev/test/internal/services/model_service.go:39
[0m[33m[59.384ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 23:29:43 model_service.go:215: 🔍 [HTTP-REQUEST] 模型: qwen-plus
2025/06/17 23:29:43 model_service.go:216: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025/06/17 23:29:43 model_service.go:217: 🔍 [HTTP-REQUEST] 请求体长度: 719 bytes
2025/06/17 23:29:43 model_service.go:222: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "你是一个专业的题目解答助手，请根据题目内容给出正确答案和详细解析。",
        "role": "system"
      },
      {
        "content": "请解答这道题目，返回JSON格式：{\"answer\":\"答案\",\"analysis\":\"解析内容\"}\n\n题目内容：题目类型：单选题\n题干：(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\n选项：map[A:左侧A柱盲区内可能有行人将要通过 B:对向车道车辆将要调头 C:后面有车辆将超车 D:右侧车道有车辆将要通过]",
        "role": "user"
      }
    ]
  },
  "model": "qwen-plus",
  "parameters": {
    "presence_penalty": 0,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.3,
    "top_k": 50,
    "top_p": 0.9
  }
}
2025/06/17 23:29:43 model_service.go:241: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/17 23:29:43 model_service.go:242: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/17 23:29:49 model_service.go:263: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/17 23:29:49 model_service.go:264: 🔍 [HTTP-RESPONSE] 响应体长度: 839 bytes
2025/06/17 23:29:49 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/17 23:29:49 model_service.go:270: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "finish_reason": "stop",
    "text": "```json\n{\n  \"answer\": \"A\",\n  \"analysis\": \"解析内容：根据题干描述，驾车遇到此情况时，需要特别注意左侧A柱盲区内可能有行人将要通过。A柱是汽车前挡风玻璃两侧的支柱，容易形成视觉盲区，尤其是在转弯或观察侧面情况时，可能会遮挡行人或其他障碍物。因此，选项A是正确答案。其他选项中，B（对向车道车辆将要调头）、C（后面有车辆将超车）和D（右侧车道有车辆将要通过）虽然可能是驾驶中需要注意的情况，但与题目中提到的‘A柱盲区’无关，因此排除。\"\n}\n```"
  },
  "usage": {
    "total_tokens": 277,
    "output_tokens": 149,
    "input_tokens": 128,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "d301172e-ad23-98c6-b5f2-d9031bdc793c"
}
2025/06/17 23:29:49 model_service.go:282: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/17 23:29:49 model_service.go:286: 🔍 [HTTP-DEBUG] Choices为空，尝试处理Solve模型格式
2025/06/17 23:29:49 model_service.go:293: 🔍 [HTTP-DEBUG] 从output.text提取内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：根据题干描述，驾车遇到此情况�
2025/06/17 23:29:49 model_service.go:309: 🔍 [HTTP-DEBUG] 成功转换为标准格式
2025/06/17 23:29:49 model_service.go:160: 🔍 [SOLVE-DEBUG] 原始响应Choices数量: 1
2025/06/17 23:29:49 model_service.go:161: 🔍 [SOLVE-DEBUG] 原始响应Usage: {TotalTokens:277}
2025/06/17 23:29:49 model_service.go:163: 🔍 [SOLVE-DEBUG] 第一个Choice内容类型: string
2025/06/17 23:29:49 model_service.go:164: 🔍 [SOLVE-DEBUG] 第一个Choice内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：根据题干描述，驾车遇到此情况时，需要特别注意左侧A柱盲区内可能有行人将要通过。A柱是汽车前挡风玻璃两侧的支柱，容易形成视觉盲区，尤其是在转弯或观察侧面情况时，可能会遮挡行人或其他障碍物。因此，选项A是正确答案。其他选项中，B（对向车道车辆将要调头）、C（后面有车辆将超车）和D（右侧车道有车辆将要通过）虽然可能是驾驶中需要注意的情况，但与题目中提到的‘A柱盲区’无关，因此排除。"
}
```
2025/06/17 23:29:49 solve_service.go:121: ❌ [SOLVE] Solve模型处理失败: failed to parse solve response JSON: invalid character '`' looking for beginning of value
2025/06/17 23:29:49 solve_handler.go:74: 📝 [HANDLER] 记录解题日志

2025/06/17 23:29:49 [32m/Users/<USER>/Documents/Dev/test/internal/services/log_service.go:41
[0m[33m[123.310ms] [34;1m[rows:1][0m INSERT INTO `hook_solve_logs` (`app_id`,`user_url`,`matched_id`,`ocr_token`,`source`,`data`,`status`,`latency`,`created_at`) VALUES ('NdOCNqqWPtrLFvZj','http://img.igmdns.com/images/cc0001.jpg',0,1030,'',(NULL),0,8804,'2025-06-17 23:29:49.663')
2025/06/17 23:29:49 solve_handler.go:115: ❌ [HANDLER] 解题处理失败 - Status: 0, Message: failed to parse solve response JSON: invalid character '`' looking for beginning of value
time="2025-06-17T23:29:49+08:00" level=info msg="HTTP Request" body_size=114 client_ip="::1" error= latency=9.050895333s method=POST path=/api/v1/solve/question status_code=200 timestamp="2025-06-17T23:29:49+08:00" user_agent=PostmanRuntime-ApipostRuntime/1.1.0
[GIN] 2025/06/17 - 23:29:49 | 200 |  9.051001042s |             ::1 | POST     "/api/v1/solve/question"

2025/06/17 23:30:33 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:95
[0m[33m[58.413ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' AND app_secret = 'VsubbUlgZejHgRrylJ0qs3A9HJy3a8P2' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 23:30:33 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:111
[0m[33m[59.854ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 23:30:33 solve_handler.go:31: 🎯 [HANDLER] 收到解题请求 - IP: ::1, UserAgent: PostmanRuntime-ApipostRuntime/1.1.0
2025/06/17 23:30:33 solve_handler.go:63: ✅ [HANDLER] 请求参数解析成功 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:30:33 solve_handler.go:66: 🚀 [HANDLER] 开始处理解题请求
2025/06/17 23:30:33 solve_service.go:62: 🚀 [SOLVE] 开始处理解题请求 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:30:33 solve_service.go:65: 📋 [SOLVE] 步骤1: 验证应用和用户

2025/06/17 23:30:33 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:53
[0m[33m[59.422ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 23:30:33 [32m/Users/<USER>/Documents/Dev/test/internal/services/app_service.go:111
[0m[33m[60.417ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 23:30:33 solve_service.go:71: ✅ [SOLVE] 应用/用户验证成功 - UserID: 20, Balance: 500000
2025/06/17 23:30:33 solve_service.go:74: 💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: 500000
2025/06/17 23:30:33 solve_service.go:82: 🔍 [SOLVE] 步骤3: 调用OCR模型识别图片
2025/06/17 23:30:33 solve_service.go:175: 🔍 [OCR] 开始OCR处理 - 模型: qwen-vl-plus, 图片URL: http://img.igmdns.com/images/cc0001.jpg

2025/06/17 23:30:33 [32m/Users/<USER>/Documents/Dev/test/internal/services/model_service.go:39
[0m[33m[60.229ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-vl-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 23:30:33 model_service.go:215: 🔍 [HTTP-REQUEST] 模型: qwen-vl-plus
2025/06/17 23:30:33 model_service.go:216: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation
2025/06/17 23:30:33 model_service.go:217: 🔍 [HTTP-REQUEST] 请求体长度: 573 bytes
2025/06/17 23:30:33 model_service.go:222: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}",
        "role": "system"
      },
      {
        "content": [
          {
            "image": "http://img.igmdns.com/images/cc0001.jpg"
          },
          {
            "text": "精准且完整的识别问题,严格按照要求输出的完整json,其中的options字段的格式必须正确,选项字母为键，选项内容为值"
          }
        ],
        "role": "user"
      }
    ]
  },
  "model": "qwen-vl-plus",
  "parameters": {
    "presence_penalty": 1,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.2,
    "top_k": 1,
    "top_p": 0.01
  }
}
2025/06/17 23:30:33 model_service.go:241: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/17 23:30:33 model_service.go:242: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/17 23:30:35 model_service.go:263: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/17 23:30:35 model_service.go:264: 🔍 [HTTP-RESPONSE] 响应体长度: 755 bytes
2025/06/17 23:30:35 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/17 23:30:35 model_service.go:270: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "choices": [
      {
        "finish_reason": "stop",
        "message": {
          "role": "assistant",
          "content": [
            {
              "text": "```json\n{\n    \"qutext\": \"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\",\n    \"options\": {\n        \"A\": \"左侧A柱盲区内可能有行人将要通过\",\n        \"B\": \"对向车道车辆将要调头\",\n        \"C\": \"后面有车辆将超车\",\n        \"D\": \"右侧车道有车辆将要通过\"\n    }\n}\n```"
            }
          ]
        }
      }
    ]
  },
  "usage": {
    "input_tokens_details": {
      "text_tokens": 72,
      "image_tokens": 860
    },
    "total_tokens": 1030,
    "output_tokens": 98,
    "input_tokens": 932,
    "output_tokens_details": {
      "text_tokens": 98
    },
    "image_tokens": 860,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "e96c0375-168a-94f2-b177-d203b47a0432"
}
2025/06/17 23:30:35 model_service.go:282: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/17 23:30:35 solve_service.go:183: ✅ [OCR] 模型调用成功 - Token消耗: 1030
2025/06/17 23:30:35 solve_service.go:189: 📝 [OCR] 开始解析OCR响应
2025/06/17 23:30:35 parser_service.go:30: 🔍 [OCR-DEBUG] Content类型: []interface {}
2025/06/17 23:30:35 parser_service.go:39: 🔍 [OCR-DEBUG] Array类型，元素数量: 1
2025/06/17 23:30:35 parser_service.go:42: 🔍 [OCR-DEBUG] Array[0]类型: map[string]interface {}
2025/06/17 23:30:35 parser_service.go:47: 🔍 [OCR-DEBUG] 提取文本: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:30:35 parser_service.go:59: 🔍 [OCR-DEBUG] 最终内容长度: 332
2025/06/17 23:30:35 parser_service.go:60: 🔍 [OCR-DEBUG] 最终内容前200字符: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:30:35 parser_service.go:62: 🔍 [OCR-DEBUG] 最终内容后200字符: : "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:30:35 parser_service.go:279: 🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 23:30:35 parser_service.go:290: 🔍 [JSON-EXTRACT] 从```json```中提取: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:30:35 parser_service.go:320: 🔍 [FIELD-MAP] 开始字段映射，原始JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:30:35 parser_service.go:325: 🔍 [FIELD-MAP] 映射完成，结果: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:30:35 parser_service.go:70: 🔍 [OCR-DEBUG] 字段映射后的JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 23:30:35 parser_service.go:80: ✅ [OCR-PARSE] JSON解析成功
2025/06/17 23:30:35 parser_service.go:81: 📋 [OCR-PARSE] 解析结果 - QuText: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:30:35 parser_service.go:82: 📋 [OCR-PARSE] 解析结果 - Options数量: 4
2025/06/17 23:30:35 parser_service.go:84: 📋 [OCR-PARSE] 选项 A: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:30:35 parser_service.go:84: 📋 [OCR-PARSE] 选项 B: 对向车道车辆将要调头
2025/06/17 23:30:35 parser_service.go:84: 📋 [OCR-PARSE] 选项 C: 后面有车辆将超车
2025/06/17 23:30:35 parser_service.go:84: 📋 [OCR-PARSE] 选项 D: 右侧车道有车辆将要通过
2025/06/17 23:30:35 parser_service.go:88: 🔍 [TYPE-DETECT] 开始检测题目类型，题干内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:30:35 parser_service.go:160: 🔍 [TYPE-DETECT] 原始内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:30:35 parser_service.go:162: 🔍 [TYPE-DETECT] 转换小写后: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:30:35 parser_service.go:165: 🔍 [TYPE-DETECT] 检测到单选题
2025/06/17 23:30:35 parser_service.go:90: 🔍 [TYPE-DETECT] 检测结果: 单选题
2025/06/17 23:30:35 parser_service.go:97: 🧹 [DATA-CLEAN] 开始清洗题干内容（二次提纯）
2025/06/17 23:30:35 parser_service.go:98: 🧹 [DATA-CLEAN] 原始题干: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:30:35 parser_service.go:190: 🧹 [DATA-CLEAN] 第一步清除前缀后: 如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:30:35 parser_service.go:195: 🧹 [DATA-CLEAN] 第二步清理标点后: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:30:35 parser_service.go:100: 🧹 [DATA-CLEAN] 二次提纯完成，最终题干: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:30:35 parser_service.go:103: 🧹 [DATA-CLEAN] 开始清洗选项内容
2025/06/17 23:30:35 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 A 清理标点前: 左侧A柱盲区内可能有行人将要通过, 清理后: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:30:35 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 B 清理标点前: 对向车道车辆将要调头, 清理后: 对向车道车辆将要调头
2025/06/17 23:30:35 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 C 清理标点前: 后面有车辆将超车, 清理后: 后面有车辆将超车
2025/06/17 23:30:35 parser_service.go:209: 🧹 [DATA-CLEAN] 选项 D 清理标点前: 右侧车道有车辆将要通过, 清理后: 右侧车道有车辆将要通过
2025/06/17 23:30:35 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 A: 左侧A柱盲区内可能有行人将要通过
2025/06/17 23:30:35 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 B: 对向车道车辆将要调头
2025/06/17 23:30:35 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 C: 后面有车辆将超车
2025/06/17 23:30:35 parser_service.go:106: 🧹 [DATA-CLEAN] 清洗后选项 D: 右侧车道有车辆将要通过
2025/06/17 23:30:35 parser_service.go:111: 📏 [DATA-CALC] 题干字符长度: 18
2025/06/17 23:30:35 parser_service.go:115: 🔑 [DATA-CALC] 生成哈希键: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:30:35 parser_service.go:129: 📦 [SAVE-QUESTION] 构建完成
2025/06/17 23:30:35 parser_service.go:130: 📦 [SAVE-QUESTION] Type: 单选题
2025/06/17 23:30:35 parser_service.go:131: 📦 [SAVE-QUESTION] Content: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 23:30:35 parser_service.go:132: 📦 [SAVE-QUESTION] CleanContent: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:30:35 parser_service.go:133: 📦 [SAVE-QUESTION] UserURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:30:35 parser_service.go:134: 📦 [SAVE-QUESTION] QuestionLen: 18
2025/06/17 23:30:35 parser_service.go:135: 📦 [SAVE-QUESTION] HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:30:35 parser_service.go:136: 📦 [SAVE-QUESTION] Options数量: 4
2025/06/17 23:30:35 solve_service.go:195: ✅ [OCR] 响应解析成功 - 题目类型: 单选题, 内容长度: 76
2025/06/17 23:30:35 solve_service.go:199: 🔄 [CONTEXT] ProcessContext已更新
2025/06/17 23:30:35 solve_service.go:200: 🔄 [CONTEXT] SaveQuestion.Type: 单选题
2025/06/17 23:30:35 solve_service.go:201: 🔄 [CONTEXT] SaveQuestion.HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:30:35 solve_service.go:202: 🔄 [CONTEXT] SaveQuestion.QuestionLen: 18
2025/06/17 23:30:35 solve_service.go:203: 🔄 [CONTEXT] SaveQuestion.UserURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 23:30:35 solve_service.go:204: 🔄 [CONTEXT] OCRToken: 1030
2025/06/17 23:30:35 solve_service.go:205: 🔄 [CONTEXT] 当前处理阶段: OCR解析完成，准备进入缓存检查阶段
2025/06/17 23:30:35 solve_service.go:88: ✅ [SOLVE] OCR处理成功 - Token消耗: 1030, 题目类型: 单选题
2025/06/17 23:30:35 solve_service.go:91: 🔄 [SOLVE] 步骤4: 检查Redis缓存
2025/06/17 23:30:35 solve_service.go:212: 🔍 [CACHE-CHECK] 开始检查Redis缓存
2025/06/17 23:30:35 solve_service.go:213: 🔍 [CACHE-CHECK] 使用HashKey: 06b2720385c5e4c0df2405cbcbf815ab
2025/06/17 23:30:36 solve_service.go:217: ⚪ [CACHE-CHECK] Redis缓存未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:30:36 solve_service.go:218: 🔄 [CONTEXT] 缓存检查结果: 未命中，继续下一步
2025/06/17 23:30:36 solve_service.go:96: ⚪ [SOLVE] Redis缓存未命中
2025/06/17 23:30:36 solve_service.go:99: 🎯 [SOLVE] 步骤5: 检查MySQL精确匹配
2025/06/17 23:30:36 solve_service.go:240: 🔍 [MYSQL-EXACT] 开始MySQL精确匹配
2025/06/17 23:30:36 solve_service.go:241: 🔍 [MYSQL-EXACT] 使用HashKey: 06b2720385c5e4c0df2405cbcbf815ab

2025/06/17 23:30:36 [32m/Users/<USER>/Documents/Dev/test/internal/services/question_service.go:23
[0m[33m[59.191ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE hash_key = '06b2720385c5e4c0df2405cbcbf815ab'
2025/06/17 23:30:36 solve_service.go:245: ⚪ [MYSQL-EXACT] MySQL精确匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:30:36 solve_service.go:246: 🔄 [CONTEXT] 精确匹配结果: 未命中，继续模糊匹配
2025/06/17 23:30:36 solve_service.go:104: ⚪ [SOLVE] MySQL精确匹配未命中
2025/06/17 23:30:36 solve_service.go:107: 🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配
2025/06/17 23:30:36 solve_service.go:278: 🔍 [MYSQL-FUZZY] 开始MySQL模糊匹配
2025/06/17 23:30:36 solve_service.go:279: 🔍 [MYSQL-FUZZY] 题目类型: 单选题
2025/06/17 23:30:36 solve_service.go:280: 🔍 [MYSQL-FUZZY] 清洗后内容: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:30:36 solve_service.go:281: 🔍 [MYSQL-FUZZY] 题目长度: 18
2025/06/17 23:30:36 match_service.go:30: 🔍 [FUZZY-MATCH] 开始三步筛选匹配

2025/06/17 23:30:36 [32m/Users/<USER>/Documents/Dev/test/internal/services/match_service.go:41
[0m[33m[59.524ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE verified = true AND type = 2 AND ABS(question_len - 18) <= 5
2025/06/17 23:30:36 match_service.go:47: 🔍 [STEP-1] MySQL筛选完成 - 找到0个候选题目
2025/06/17 23:30:36 solve_service.go:285: ⚪ [MYSQL-FUZZY] MySQL模糊匹配未命中 - Error: <nil>, Questions数量: 0
2025/06/17 23:30:36 solve_service.go:286: 🔄 [CONTEXT] 模糊匹配结果: 未命中，需要调用AI模型
2025/06/17 23:30:36 solve_service.go:112: ⚪ [SOLVE] MySQL模糊匹配未命中
2025/06/17 23:30:36 solve_service.go:115: 🤖 [SOLVE] 步骤7: 调用Solve模型解答
2025/06/17 23:30:36 solve_service.go:374: 📦 [SAVE-QUESTION] CleanContent: 如图所示驾车遇到此情况时应当注意什么
2025/06/17 23:30:36 solve_service.go:396: ✅ [LOG-FILE] CleanContent已记录到 logs/clean_content.log

2025/06/17 23:30:36 [32m/Users/<USER>/Documents/Dev/test/internal/services/model_service.go:39
[0m[33m[59.621ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 23:30:36 model_service.go:215: 🔍 [HTTP-REQUEST] 模型: qwen-plus
2025/06/17 23:30:36 model_service.go:216: 🔍 [HTTP-REQUEST] 请求URL: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
2025/06/17 23:30:36 model_service.go:217: 🔍 [HTTP-REQUEST] 请求体长度: 719 bytes
2025/06/17 23:30:36 model_service.go:222: 🔍 [HTTP-REQUEST] 完整请求体:
{
  "input": {
    "messages": [
      {
        "content": "你是一个专业的题目解答助手，请根据题目内容给出正确答案和详细解析。",
        "role": "system"
      },
      {
        "content": "请解答这道题目，返回JSON格式：{\"answer\":\"答案\",\"analysis\":\"解析内容\"}\n\n题目内容：题目类型：单选题\n题干：(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\n选项：map[A:左侧A柱盲区内可能有行人将要通过 B:对向车道车辆将要调头 C:后面有车辆将超车 D:右侧车道有车辆将要通过]",
        "role": "user"
      }
    ]
  },
  "model": "qwen-plus",
  "parameters": {
    "presence_penalty": 0,
    "repetition_penalty": 1,
    "result_format": "json_object",
    "temperature": 0.3,
    "top_k": 50,
    "top_p": 0.9
  }
}
2025/06/17 23:30:36 model_service.go:241: 🔍 [HTTP-REQUEST] 请求头 Content-Type: application/json
2025/06/17 23:30:36 model_service.go:242: 🔍 [HTTP-REQUEST] 请求头 Authorization: Bearer sk-21d8cdf***
2025/06/17 23:30:42 model_service.go:263: 🔍 [HTTP-RESPONSE] 响应状态码: 200
2025/06/17 23:30:42 model_service.go:264: 🔍 [HTTP-RESPONSE] 响应体长度: 774 bytes
2025/06/17 23:30:42 model_service.go:265: 🔍 [HTTP-RESPONSE] 响应头 Content-Type: application/json
2025/06/17 23:30:42 model_service.go:270: 🔍 [HTTP-RESPONSE] 完整响应体:
{
  "output": {
    "finish_reason": "stop",
    "text": "```json\n{\n  \"answer\": \"A\",\n  \"analysis\": \"解析内容：根据题干描述，此题考察驾驶过程中需要注意的安全事项。图中显示驾车时左侧A柱可能会形成盲区，而盲区内可能有行人即将通过，这是驾驶员需要特别注意的情况。其他选项中，B选项‘对向车道车辆将要调头’、C选项‘后面有车辆将超车’、D选项‘右侧车道有车辆将要通过’，均无法直接从题目描述或常规驾驶情境中确定为最需要注意的事项，因此正确答案为A。\"\n}\n```"
  },
  "usage": {
    "total_tokens": 257,
    "output_tokens": 129,
    "input_tokens": 128,
    "prompt_tokens_details": {
      "cached_tokens": 0
    }
  },
  "request_id": "bd277b62-b473-90c0-ba35-f5946f80d2b6"
}
2025/06/17 23:30:42 model_service.go:282: ✅ [HTTP-RESPONSE] JSON解析成功
2025/06/17 23:30:42 model_service.go:286: 🔍 [HTTP-DEBUG] Choices为空，尝试处理Solve模型格式
2025/06/17 23:30:42 model_service.go:293: 🔍 [HTTP-DEBUG] 从output.text提取内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：根据题干描述，此题考察驾驶过�
2025/06/17 23:30:42 model_service.go:309: 🔍 [HTTP-DEBUG] 成功转换为标准格式
2025/06/17 23:30:42 model_service.go:160: 🔍 [SOLVE-DEBUG] 原始响应Choices数量: 1
2025/06/17 23:30:42 model_service.go:161: 🔍 [SOLVE-DEBUG] 原始响应Usage: {TotalTokens:257}
2025/06/17 23:30:42 model_service.go:163: 🔍 [SOLVE-DEBUG] 第一个Choice内容类型: string
2025/06/17 23:30:42 model_service.go:164: 🔍 [SOLVE-DEBUG] 第一个Choice内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：根据题干描述，此题考察驾驶过程中需要注意的安全事项。图中显示驾车时左侧A柱可能会形成盲区，而盲区内可能有行人即将通过，这是驾驶员需要特别注意的情况。其他选项中，B选项‘对向车道车辆将要调头’、C选项‘后面有车辆将超车’、D选项‘右侧车道有车辆将要通过’，均无法直接从题目描述或常规驾驶情境中确定为最需要注意的事项，因此正确答案为A。"
}
```
2025/06/17 23:30:42 solve_service.go:121: ❌ [SOLVE] Solve模型处理失败: failed to parse solve response JSON: invalid character '`' looking for beginning of value
2025/06/17 23:30:42 solve_handler.go:74: 📝 [HANDLER] 记录解题日志

2025/06/17 23:30:42 [32m/Users/<USER>/Documents/Dev/test/internal/services/log_service.go:41
[0m[33m[122.542ms] [34;1m[rows:1][0m INSERT INTO `hook_solve_logs` (`app_id`,`user_url`,`matched_id`,`ocr_token`,`source`,`data`,`status`,`latency`,`created_at`) VALUES ('NdOCNqqWPtrLFvZj','http://img.igmdns.com/images/cc0001.jpg',0,1030,'',(NULL),0,9412,'2025-06-17 23:30:42.857')
2025/06/17 23:30:42 solve_handler.go:115: ❌ [HANDLER] 解题处理失败 - Status: 0, Message: failed to parse solve response JSON: invalid character '`' looking for beginning of value
time="2025-06-17T23:30:42+08:00" level=info msg="HTTP Request" body_size=114 client_ip="::1" error= latency=9.654410916s method=POST path=/api/v1/solve/question status_code=200 timestamp="2025-06-17T23:30:42+08:00" user_agent=PostmanRuntime-ApipostRuntime/1.1.0
[GIN] 2025/06/17 - 23:30:42 | 200 |  9.654521166s |             ::1 | POST     "/api/v1/solve/question"
2025/06/17 23:30:42 solve_handler.go:115: ❌ [HANDLER] 解题处理失败 - Status: 0, Message: failed to parse solve response JSON: invalid character '`' looking for beginning of value
