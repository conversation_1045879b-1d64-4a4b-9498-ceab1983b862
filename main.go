package main

import (
	"io"
	"log"
	"os"
	"solve-go-api/internal/config"
	"solve-go-api/internal/database"
	"solve-go-api/internal/router"
	"solve-go-api/internal/services"

	"github.com/joho/godotenv"
)

// setupLogging 设置日志输出到文件
func setupLogging() {
	// 创建logs目录
	if err := os.MkdirAll("logs", 0755); err != nil {
		log.Printf("Failed to create logs directory: %v", err)
		return
	}

	// 打开日志文件
	logFile, err := os.OpenFile("logs/service.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("Failed to open log file: %v", err)
		return
	}

	// 设置日志输出到文件和终端
	multiWriter := io.MultiWriter(os.Stdout, logFile)
	log.SetOutput(multiWriter)

	// 设置日志格式
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	log.Printf("📝 [SYSTEM] 日志系统初始化完成，日志将同时输出到终端和文件: logs/service.log")
}

func main() {
	// 设置日志输出到文件
	setupLogging()

	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库连接
	db, err := database.InitMySQL(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to MySQL: %v", err)
	}

	// 获取底层sql.DB用于关闭连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB: %v", err)
	}
	defer sqlDB.Close()

	// 初始化Redis连接
	rdb, err := database.InitRedis(cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer rdb.Close()

	// 运行数据库迁移
	if err := database.Migrate(db); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化系统数据
	if err := database.InitSystemData(db); err != nil {
		log.Fatalf("Failed to initialize system data: %v", err)
	}

	// 初始化服务
	serviceContainer := services.NewContainer(db, rdb, cfg)

	// 设置路由
	r := router.Setup(serviceContainer)

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.Server.Port)
	if err := r.Run(":" + cfg.Server.Port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
