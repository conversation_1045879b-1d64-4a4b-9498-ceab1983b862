#!/bin/bash

# 调试创建应用的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

PHONE="15653259315"
PASSWORD="123123"
APP_NAME="学法减分"
API_BASE_URL="http://localhost:8080/api/v1"

log_info "开始调试创建应用流程..."

# 1. 检查服务健康状态
log_info "步骤1: 检查服务健康状态..."
HEALTH_RESPONSE=$(curl -s "http://localhost:8080/health")
echo "健康检查响应: $HEALTH_RESPONSE"

if echo "$HEALTH_RESPONSE" | grep -q '"status":"ok"'; then
    log_success "服务运行正常"
else
    log_error "服务状态异常"
    exit 1
fi

# 2. 用户登录
log_info "步骤2: 用户登录..."
LOGIN_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE_URL/login" \
    -H "Content-Type: application/json" \
    -d "{\"phone\": \"$PHONE\", \"password\": \"$PASSWORD\"}")

HTTP_CODE=$(echo "$LOGIN_RESPONSE" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$LOGIN_RESPONSE" | sed -E 's/HTTPSTATUS:[0-9]*$//')

echo "登录HTTP状态码: $HTTP_CODE"
echo "登录响应体: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ] && echo "$RESPONSE_BODY" | grep -q '"code":200'; then
    log_success "用户登录成功"
    
    # 提取token和用户信息
    TOKEN=$(echo "$RESPONSE_BODY" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    USER_ID=$(echo "$RESPONSE_BODY" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    NICKNAME=$(echo "$RESPONSE_BODY" | grep -o '"nickname":"[^"]*"' | cut -d'"' -f4)
    ROLE=$(echo "$RESPONSE_BODY" | grep -o '"role":"[^"]*"' | cut -d'"' -f4)
    BALANCE=$(echo "$RESPONSE_BODY" | grep -o '"balance":[0-9]*' | cut -d':' -f2)
    
    log_info "用户信息: ID=$USER_ID, 昵称=$NICKNAME, 角色=$ROLE, 余额=$BALANCE"
    log_info "Token: ${TOKEN:0:30}..."
    
    if [ -z "$TOKEN" ]; then
        log_error "无法提取登录token"
        exit 1
    fi
    
else
    log_error "用户登录失败"
    echo "HTTP状态码: $HTTP_CODE"
    echo "响应内容: $RESPONSE_BODY"
    exit 1
fi

# 3. 测试JWT token验证
log_info "步骤3: 测试JWT token验证..."
PROFILE_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$API_BASE_URL/user/profile" \
    -H "Authorization: Bearer $TOKEN")

PROFILE_HTTP_CODE=$(echo "$PROFILE_RESPONSE" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
PROFILE_BODY=$(echo "$PROFILE_RESPONSE" | sed -E 's/HTTPSTATUS:[0-9]*$//')

echo "获取用户信息HTTP状态码: $PROFILE_HTTP_CODE"
echo "获取用户信息响应: $PROFILE_BODY"

if [ "$PROFILE_HTTP_CODE" = "200" ]; then
    log_success "JWT token验证成功"
else
    log_error "JWT token验证失败"
    exit 1
fi

# 4. 测试获取应用列表
log_info "步骤4: 测试获取应用列表..."
APPS_LIST_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$API_BASE_URL/apps/" \
    -H "Authorization: Bearer $TOKEN")

APPS_HTTP_CODE=$(echo "$APPS_LIST_RESPONSE" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
APPS_BODY=$(echo "$APPS_LIST_RESPONSE" | sed -E 's/HTTPSTATUS:[0-9]*$//')

echo "获取应用列表HTTP状态码: $APPS_HTTP_CODE"
echo "获取应用列表响应: $APPS_BODY"

if [ "$APPS_HTTP_CODE" = "200" ]; then
    log_success "获取应用列表成功"
    
    # 检查是否已有应用
    if echo "$APPS_BODY" | grep -q "\"$APP_NAME\""; then
        log_warning "应用 '$APP_NAME' 已存在"
    else
        log_info "应用 '$APP_NAME' 不存在，可以创建"
    fi
else
    log_error "获取应用列表失败"
    exit 1
fi

# 5. 创建应用
log_info "步骤5: 创建应用..."
CREATE_APP_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$API_BASE_URL/apps/" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "{\"name\": \"$APP_NAME\"}")

CREATE_HTTP_CODE=$(echo "$CREATE_APP_RESPONSE" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
CREATE_BODY=$(echo "$CREATE_APP_RESPONSE" | sed -E 's/HTTPSTATUS:[0-9]*$//')

echo "创建应用HTTP状态码: $CREATE_HTTP_CODE"
echo "创建应用响应: $CREATE_BODY"

if [ "$CREATE_HTTP_CODE" = "200" ] && echo "$CREATE_BODY" | grep -q '"code":200'; then
    log_success "应用创建成功"
    
    # 提取应用信息
    APP_ID=$(echo "$CREATE_BODY" | grep -o '"app_id":"[^"]*"' | cut -d'"' -f4)
    APP_SECRET=$(echo "$CREATE_BODY" | grep -o '"app_secret":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$APP_ID" ] && [ -n "$APP_SECRET" ]; then
        echo ""
        echo "🎉 应用创建成功！"
        echo "================================"
        echo "应用信息:"
        echo "  应用名称: $APP_NAME"
        echo "  应用ID: $APP_ID"
        echo "  应用密钥: $APP_SECRET"
        echo "  所属用户: $NICKNAME ($PHONE)"
        echo "================================"
    else
        log_error "无法提取应用ID或密钥"
        echo "响应内容: $CREATE_BODY"
    fi
    
elif [ "$CREATE_HTTP_CODE" = "200" ] && echo "$CREATE_BODY" | grep -q '"code":400'; then
    log_error "应用创建失败 - 请求参数错误"
    echo "错误信息: $(echo "$CREATE_BODY" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)"
    
elif [ "$CREATE_HTTP_CODE" = "401" ]; then
    log_error "应用创建失败 - 认证失败"
    echo "可能的原因: JWT token无效或过期"
    
elif [ "$CREATE_HTTP_CODE" = "500" ]; then
    log_error "应用创建失败 - 服务器内部错误"
    echo "错误信息: $(echo "$CREATE_BODY" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)"
    
else
    log_error "应用创建失败"
    echo "HTTP状态码: $CREATE_HTTP_CODE"
    echo "响应内容: $CREATE_BODY"
fi

# 6. 再次获取应用列表验证
log_info "步骤6: 验证应用是否创建成功..."
FINAL_APPS_RESPONSE=$(curl -s -X GET "$API_BASE_URL/apps/" \
    -H "Authorization: Bearer $TOKEN")

echo "最终应用列表: $FINAL_APPS_RESPONSE"

if echo "$FINAL_APPS_RESPONSE" | grep -q "\"$APP_NAME\""; then
    log_success "应用已成功添加到用户的应用列表中"
else
    log_warning "应用可能未正确添加到列表中"
fi

log_info "调试完成！"
