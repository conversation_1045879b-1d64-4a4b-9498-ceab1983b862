#!/bin/bash

# 启动脚本
set -e

echo "Starting Solve Go API..."

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

# 加载环境变量
export $(cat .env | grep -v '^#' | xargs)

# 检查必要的环境变量
required_vars=("DB_HOST" "DB_USERNAME" "DB_PASSWORD" "DB_DATABASE" "REDIS_HOST")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required environment variable $var is not set."
        exit 1
    fi
done

# 检查数据库连接
echo "Checking database connection..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Error: Cannot connect to MySQL database."
    exit 1
fi

# 检查Redis连接
echo "Checking Redis connection..."
redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Error: Cannot connect to Redis."
    exit 1
fi

# 构建应用
echo "Building application..."
go build -o solve-api main.go

# 启动应用
echo "Starting server on port ${SERVER_PORT:-8080}..."
./solve-api
