#!/bin/bash

# 为用户创建应用的脚本
# 用法: ./create_user_app.sh <phone> <password> <app_name>

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 3 ]; then
    echo "用法: $0 <phone> <password> <app_name>"
    echo "示例: $0 15653259315 123123 学法减分"
    exit 1
fi

PHONE="$1"
PASSWORD="$2"
APP_NAME="$3"
API_BASE_URL="http://localhost:8080/api/v1"

log_info "开始为用户 $PHONE 创建应用: $APP_NAME"

# 1. 用户登录获取token
log_info "步骤1: 用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE_URL/login" \
    -H "Content-Type: application/json" \
    -d "{\"phone\": \"$PHONE\", \"password\": \"$PASSWORD\"}")

echo "登录响应: $LOGIN_RESPONSE"

# 检查登录是否成功
if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    log_success "用户登录成功"
    
    # 提取token
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -z "$TOKEN" ]; then
        log_error "无法提取登录token"
        exit 1
    fi
    
    log_info "Token: ${TOKEN:0:20}..."
    
    # 提取用户信息
    USER_ID=$(echo "$LOGIN_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    NICKNAME=$(echo "$LOGIN_RESPONSE" | grep -o '"nickname":"[^"]*"' | cut -d'"' -f4)
    BALANCE=$(echo "$LOGIN_RESPONSE" | grep -o '"balance":[0-9]*' | cut -d':' -f2)
    
    log_info "用户信息: ID=$USER_ID, 昵称=$NICKNAME, 余额=$BALANCE"
    
else
    log_error "用户登录失败"
    echo "响应内容: $LOGIN_RESPONSE"
    exit 1
fi

# 2. 创建应用
log_info "步骤2: 创建应用..."
CREATE_APP_RESPONSE=$(curl -s -X POST "$API_BASE_URL/apps/" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "{\"name\": \"$APP_NAME\"}")

echo "创建应用响应: $CREATE_APP_RESPONSE"

# 检查应用创建是否成功
if echo "$CREATE_APP_RESPONSE" | grep -q '"code":200'; then
    log_success "应用创建成功"
    
    # 提取应用信息
    APP_ID=$(echo "$CREATE_APP_RESPONSE" | grep -o '"app_id":"[^"]*"' | cut -d'"' -f4)
    APP_SECRET=$(echo "$CREATE_APP_RESPONSE" | grep -o '"app_secret":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$APP_ID" ] && [ -n "$APP_SECRET" ]; then
        log_success "应用创建完成"
        
        echo ""
        echo "🎉 应用创建成功！"
        echo "================================"
        echo "用户信息:"
        echo "  手机号: $PHONE"
        echo "  昵称: $NICKNAME"
        echo "  用户ID: $USER_ID"
        echo ""
        echo "应用信息:"
        echo "  应用名称: $APP_NAME"
        echo "  应用ID: $APP_ID"
        echo "  应用密钥: $APP_SECRET"
        echo ""
        echo "📋 API调用示例:"
        echo "curl -X POST http://localhost:8080/api/v1/solve/question \\"
        echo "  -H \"Content-Type: application/json\" \\"
        echo "  -d '{"
        echo "    \"app_id\": \"$APP_ID\","
        echo "    \"app_secret\": \"$APP_SECRET\","
        echo "    \"image\": \"base64编码的图片数据\","
        echo "    \"question_type\": \"choice\""
        echo "  }'"
        echo "================================"
        
    else
        log_error "无法提取应用ID或密钥"
        echo "响应内容: $CREATE_APP_RESPONSE"
        exit 1
    fi
    
else
    log_error "应用创建失败"
    echo "响应内容: $CREATE_APP_RESPONSE"
    exit 1
fi

# 3. 验证应用列表
log_info "步骤3: 验证应用列表..."
APPS_LIST_RESPONSE=$(curl -s -X GET "$API_BASE_URL/apps/" \
    -H "Authorization: Bearer $TOKEN")

echo "应用列表响应: $APPS_LIST_RESPONSE"

if echo "$APPS_LIST_RESPONSE" | grep -q "\"$APP_NAME\""; then
    log_success "应用已成功添加到用户的应用列表中"
else
    log_warning "应用可能未正确添加到列表中，请手动检查"
fi

log_success "任务完成！"
