package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"
)

// APIRequest 请求结构体
type APIRequest struct {
	AppID     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
	ImageURL  string `json:"image_url"`
}

// APIResponse 响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// TestConfig 测试配置
type TestConfig struct {
	BaseURL   string
	AppID     string
	AppSecret string
	ImageBase string
	StartNum  int
	EndNum    int
	Threads   int
}

// TestResult 测试结果
type TestResult struct {
	ImageURL   string
	Success    bool
	StatusCode int
	Message    string
	Duration   time.Duration
	ThreadID   int
}

// Logger 日志记录器
type Logger struct {
	successFile *os.File
	failureFile *os.File
	mutex       sync.Mutex
}

func main() {
	// 解析命令行参数
	startNum := 1
	endNum := 1000
	threads := 10

	if len(os.Args) > 1 {
		if num, err := strconv.Atoi(os.Args[1]); err == nil {
			startNum = num
		}
	}
	if len(os.Args) > 2 {
		if num, err := strconv.Atoi(os.Args[2]); err == nil {
			endNum = num
		}
	}
	if len(os.Args) > 3 {
		if num, err := strconv.Atoi(os.Args[3]); err == nil {
			threads = num
		}
	}

	// 测试配置
	config := TestConfig{
		BaseURL:   "http://localhost:8080/api/v1/solve/question",
		AppID:     "NdOCNqqWPtrLFvZj",
		AppSecret: "VsubbUlgZejHgRrylJ0qs3A9HJy3a8P2",
		ImageBase: "http://img.igmdns.com/images/cc",
		StartNum:  startNum,
		EndNum:    endNum,
		Threads:   threads,
	}

	// 初始化日志记录器
	logger, err := NewLogger()
	if err != nil {
		log.Fatalf("初始化日志记录器失败: %v", err)
	}
	defer logger.Close()

	fmt.Printf("🚀 开始并发测试拍照搜题API\n")
	fmt.Printf("📋 配置信息:\n")
	fmt.Printf("   - API地址: %s\n", config.BaseURL)
	fmt.Printf("   - 图片范围: %04d - %04d\n", config.StartNum, config.EndNum)
	fmt.Printf("   - 线程数量: %d\n", config.Threads)
	fmt.Printf("   - 总请求数: %d\n", config.EndNum-config.StartNum+1)

	// 创建任务队列
	tasks := make(chan int, config.EndNum-config.StartNum+1)
	results := make(chan TestResult, config.EndNum-config.StartNum+1)

	// 填充任务队列
	for i := config.StartNum; i <= config.EndNum; i++ {
		tasks <- i
	}
	close(tasks)

	// 启动工作线程
	var wg sync.WaitGroup
	startTime := time.Now()

	for threadID := 1; threadID <= config.Threads; threadID++ {
		wg.Add(1)
		go worker(threadID, tasks, results, config, logger, &wg)
	}

	// 等待所有工作线程完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	var allResults []TestResult
	successCount := 0
	failureCount := 0

	for result := range results {
		allResults = append(allResults, result)
		if result.Success {
			successCount++
		} else {
			failureCount++
		}

		// 实时显示进度
		total := len(allResults)
		if total%50 == 0 || total == config.EndNum-config.StartNum+1 {
			fmt.Printf("📊 进度: %d/%d (成功:%d, 失败:%d)\n",
				total, config.EndNum-config.StartNum+1, successCount, failureCount)
		}
	}

	// 显示最终统计
	totalDuration := time.Since(startTime)
	fmt.Printf("\n🎉 测试完成!\n")
	fmt.Printf("📈 统计结果:\n")
	fmt.Printf("   - 总请求数: %d\n", len(allResults))
	fmt.Printf("   - 成功请求: %d (%.2f%%)\n", successCount, float64(successCount)/float64(len(allResults))*100)
	fmt.Printf("   - 失败请求: %d (%.2f%%)\n", failureCount, float64(failureCount)/float64(len(allResults))*100)
	fmt.Printf("   - 总耗时: %v\n", totalDuration)
	fmt.Printf("   - 平均QPS: %.2f\n", float64(len(allResults))/totalDuration.Seconds())

	// 计算平均响应时间
	if len(allResults) > 0 {
		var totalDur time.Duration
		for _, result := range allResults {
			totalDur += result.Duration
		}
		avgDuration := totalDur / time.Duration(len(allResults))
		fmt.Printf("   - 平均响应时间: %v\n", avgDuration)
	}

	fmt.Printf("\n📄 日志文件:\n")
	fmt.Printf("   - 成功日志: logs/api_test_success.log\n")
	fmt.Printf("   - 失败日志: logs/api_test_failure.log\n")
}

// worker 工作线程
func worker(threadID int, tasks <-chan int, results chan<- TestResult, config TestConfig, logger *Logger, wg *sync.WaitGroup) {
	defer wg.Done()

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	for imageNum := range tasks {
		imageURL := fmt.Sprintf("%s%04d.jpg", config.ImageBase, imageNum)

		startTime := time.Now()
		success, statusCode, message := testSingleRequest(client, config, imageURL)
		duration := time.Since(startTime)

		result := TestResult{
			ImageURL:   imageURL,
			Success:    success,
			StatusCode: statusCode,
			Message:    message,
			Duration:   duration,
			ThreadID:   threadID,
		}

		// 记录日志
		logger.LogResult(result)

		results <- result
	}
}

// testSingleRequest 测试单个请求
func testSingleRequest(client *http.Client, config TestConfig, imageURL string) (bool, int, string) {
	// 构建请求
	request := APIRequest{
		AppID:     config.AppID,
		AppSecret: config.AppSecret,
		ImageURL:  imageURL,
	}

	// 序列化请求体
	requestBody, err := json.Marshal(request)
	if err != nil {
		return false, 0, fmt.Sprintf("序列化请求失败: %v", err)
	}

	// 发送HTTP请求
	resp, err := client.Post(config.BaseURL, "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return false, 0, fmt.Sprintf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, resp.StatusCode, fmt.Sprintf("读取响应失败: %v", err)
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(responseBody, &apiResp); err != nil {
		return false, resp.StatusCode, fmt.Sprintf("解析响应失败: %v", err)
	}

	// 判断是否成功
	if resp.StatusCode == 200 && apiResp.Code == 200 {
		return true, resp.StatusCode, "请求成功"
	}

	return false, resp.StatusCode, apiResp.Message
}

// NewLogger 创建日志记录器
func NewLogger() (*Logger, error) {
	// 确保logs目录存在
	if err := os.MkdirAll("logs", 0755); err != nil {
		return nil, err
	}

	// 打开成功日志文件
	successFile, err := os.OpenFile("logs/api_test_success.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}

	// 打开失败日志文件
	failureFile, err := os.OpenFile("logs/api_test_failure.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		successFile.Close()
		return nil, err
	}

	return &Logger{
		successFile: successFile,
		failureFile: failureFile,
	}, nil
}

// LogResult 记录测试结果
func (l *Logger) LogResult(result TestResult) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	timestamp := time.Now().Format("2006/01/02 15:04:05")

	if result.Success {
		logEntry := fmt.Sprintf("%s [Thread-%d] SUCCESS %s - %d - %s - %v\n",
			timestamp, result.ThreadID, result.ImageURL, result.StatusCode, result.Message, result.Duration)
		l.successFile.WriteString(logEntry)
	} else {
		logEntry := fmt.Sprintf("%s [Thread-%d] FAILURE %s - %d - %s - %v\n",
			timestamp, result.ThreadID, result.ImageURL, result.StatusCode, result.Message, result.Duration)
		l.failureFile.WriteString(logEntry)
	}
}

// Close 关闭日志文件
func (l *Logger) Close() {
	if l.successFile != nil {
		l.successFile.Close()
	}
	if l.failureFile != nil {
		l.failureFile.Close()
	}
}
