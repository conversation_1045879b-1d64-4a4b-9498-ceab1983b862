package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// User 用户模型
type User struct {
	ID       uint   `gorm:"primaryKey;autoIncrement"`
	Phone    string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	Role     string `gorm:"type:enum('admin','manager','user');default:'user'"`
	Nickname string `gorm:"type:varchar(100)"`
	Balance  int64  `gorm:"default:0"`
	IsActive int    `gorm:"type:tinyint;not null;default:2;comment:用户状态:0=禁用,1=正常,2=审核中"`
}

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

// 用户状态常量
const (
	UserStatusDisabled = 0 // 禁用
	UserStatusActive   = 1 // 正常
	UserStatusPending  = 2 // 审核中
)

func main() {
	// 获取数据库连接信息
	host := getEnv("DB_HOST", "***********")
	port := getEnv("DB_PORT", "3380")
	user := getEnv("DB_USER", "gmdns")
	password := getEnv("DB_PASSWORD", "Suyan15913..")
	dbname := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbname)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("🧪 测试用户注册接口逻辑")
	fmt.Println("========================")

	// 1. 模拟 CreateUser 方法
	fmt.Println("\n📝 步骤1: 模拟 CreateUser 方法...")

	testPhone := "13666666666"
	testNickname := "API测试用户"
	testPassword := "123456"

	// 删除可能存在的测试用户
	db.Where("phone = ?", testPhone).Delete(&User{})

	// 模拟用户服务的 CreateUser 方法
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(testPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Fatal("密码加密失败:", err)
	}

	// 完全按照 user_service.go 中的逻辑创建用户
	newUser := &User{
		Phone:    testPhone,
		Password: string(hashedPassword),
		Role:     "user",
		Nickname: testNickname,
		Balance:  0,
		IsActive: UserStatusPending, // 默认审核状态，需要管理员审核
	}

	err = db.Create(newUser).Error
	if err != nil {
		log.Fatal("创建用户失败:", err)
	}

	fmt.Printf("✅ 用户创建成功:\n")
	fmt.Printf("   ID: %d\n", newUser.ID)
	fmt.Printf("   手机: %s\n", newUser.Phone)
	fmt.Printf("   昵称: %s\n", newUser.Nickname)
	fmt.Printf("   状态: %d (%s)\n", newUser.IsActive, getStatusName(newUser.IsActive))

	if newUser.IsActive != UserStatusPending {
		fmt.Printf("❌ 错误: 用户状态应该是 %d (审核中)，但实际是 %d\n", UserStatusPending, newUser.IsActive)
	} else {
		fmt.Printf("✅ 用户状态正确: %d (审核中)\n", newUser.IsActive)
	}

	// 2. 验证数据库中的实际值
	fmt.Println("\n🔍 步骤2: 验证数据库中的实际值...")

	var dbUser User
	err = db.Where("phone = ?", testPhone).First(&dbUser).Error
	if err != nil {
		log.Fatal("查询用户失败:", err)
	}

	fmt.Printf("数据库中的用户信息:\n")
	fmt.Printf("   ID: %d\n", dbUser.ID)
	fmt.Printf("   手机: %s\n", dbUser.Phone)
	fmt.Printf("   昵称: %s\n", dbUser.Nickname)
	fmt.Printf("   状态: %d (%s)\n", dbUser.IsActive, getStatusName(dbUser.IsActive))

	// 3. 测试不设置 IsActive 的情况
	fmt.Println("\n🔧 步骤3: 测试数据库默认值...")

	testPhone2 := "13555555555"
	testNickname2 := "默认值测试用户"

	// 删除可能存在的测试用户
	db.Where("phone = ?", testPhone2).Delete(&User{})

	// 不设置 IsActive，让数据库使用默认值
	hashedPassword2, _ := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	newUser2 := &User{
		Phone:    testPhone2,
		Password: string(hashedPassword2),
		Role:     "user",
		Nickname: testNickname2,
		Balance:  0,
		// 不设置 IsActive
	}

	err = db.Create(newUser2).Error
	if err != nil {
		log.Fatal("创建用户2失败:", err)
	}

	fmt.Printf("✅ 用户2创建成功:\n")
	fmt.Printf("   ID: %d\n", newUser2.ID)
	fmt.Printf("   手机: %s\n", newUser2.Phone)
	fmt.Printf("   昵称: %s\n", newUser2.Nickname)
	fmt.Printf("   状态: %d (%s)\n", newUser2.IsActive, getStatusName(newUser2.IsActive))

	// 4. 检查最近创建的用户
	fmt.Println("\n📊 步骤4: 检查最近创建的用户...")

	var recentUsers []User
	err = db.Order("created_at DESC").Limit(3).Find(&recentUsers).Error
	if err != nil {
		log.Fatal("查询最近用户失败:", err)
	}

	fmt.Println("最近创建的用户:")
	for _, u := range recentUsers {
		fmt.Printf("  - ID: %d, 手机: %s, 昵称: %s, 状态: %d (%s)\n",
			u.ID, u.Phone, u.Nickname, u.IsActive, getStatusName(u.IsActive))
	}

	// 5. 清理测试数据
	fmt.Println("\n🧹 步骤5: 清理测试数据...")

	db.Where("phone IN ?", []string{testPhone, testPhone2}).Delete(&User{})
	fmt.Printf("✅ 测试用户已清理\n")

	// 6. 结论
	fmt.Println("\n🎯 测试结论:")
	if newUser.IsActive == UserStatusPending && newUser2.IsActive == UserStatusPending {
		fmt.Println("✅ 用户注册逻辑正常，新用户状态为审核中")
		fmt.Println("✅ 数据库默认值正常工作")
		fmt.Println("\n💡 如果前端注册的用户状态还是1，可能的原因:")
		fmt.Println("   1. 前端调用的不是标准的注册接口")
		fmt.Println("   2. 有其他代码路径在创建用户")
		fmt.Println("   3. 数据库中有触发器或其他逻辑")
		fmt.Println("   4. 缓存问题")
	} else {
		fmt.Println("❌ 用户注册逻辑异常")
		fmt.Printf("   期望状态: %d (审核中)\n", UserStatusPending)
		fmt.Printf("   实际状态1: %d (%s)\n", newUser.IsActive, getStatusName(newUser.IsActive))
		fmt.Printf("   实际状态2: %d (%s)\n", newUser2.IsActive, getStatusName(newUser2.IsActive))
	}
}

// getStatusName 获取状态名称
func getStatusName(status int) string {
	switch status {
	case UserStatusDisabled:
		return "禁用"
	case UserStatusActive:
		return "正常"
	case UserStatusPending:
		return "审核中"
	default:
		return "未知"
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
