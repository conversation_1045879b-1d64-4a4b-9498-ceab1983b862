#!/bin/bash

# 拍照搜题API并发测试脚本
# 使用方法: ./run_api_test.sh [起始编号] [结束编号] [线程数]

echo "🚀 拍照搜题API并发测试工具"
echo "================================"

# 默认参数
START_NUM=${1:-1}
END_NUM=${2:-1000}
THREADS=${3:-10}

echo "📋 测试参数:"
echo "   起始编号: $START_NUM"
echo "   结束编号: $END_NUM"
echo "   线程数量: $THREADS"
echo "   总请求数: $((END_NUM - START_NUM + 1))"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 进入脚本目录
cd "$(dirname "$0")"

# 编译并运行测试程序
echo "🔨 编译测试程序..."
if go build -o api_test_concurrent test_solve_api_concurrent.go; then
    echo "✅ 编译成功"
    echo ""
    echo "🏃 开始执行测试..."
    echo "================================"
    
    # 修改程序中的参数并运行
    ./api_test_concurrent
    
    echo ""
    echo "🎉 测试完成!"
    echo "📄 查看日志文件:"
    echo "   成功日志: logs/api_test_success.log"
    echo "   失败日志: logs/api_test_failure.log"
    echo "   CleanContent日志: logs/clean_content.log"
    
    # 显示失败统计
    if [ -f "logs/api_test_failure.log" ]; then
        FAILURE_COUNT=$(wc -l < logs/api_test_failure.log)
        echo ""
        echo "❌ 失败请求数量: $FAILURE_COUNT"
        if [ $FAILURE_COUNT -gt 0 ]; then
            echo "📋 最近5个失败请求:"
            tail -5 logs/api_test_failure.log
        fi
    fi
    
    # 清理编译文件
    rm -f api_test_concurrent
else
    echo "❌ 编译失败"
    exit 1
fi
