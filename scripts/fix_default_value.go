package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 获取数据库连接信息
	host := getEnv("DB_HOST", "***********")
	port := getEnv("DB_PORT", "3380")
	user := getEnv("DB_USER", "gmdns")
	password := getEnv("DB_PASSWORD", "Suyan15913..")
	dbname := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbname)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("🔧 修复 is_active 字段默认值...")

	// 1. 检查当前默认值
	fmt.Println("\n📋 检查当前字段信息...")
	var columnInfo struct {
		Field   string  `json:"field"`
		Type    string  `json:"type"`
		Null    string  `json:"null"`
		Key     string  `json:"key"`
		Default *string `json:"default"`
		Extra   string  `json:"extra"`
	}

	err = db.Raw("SHOW COLUMNS FROM hook_user WHERE Field = 'is_active'").Scan(&columnInfo).Error
	if err != nil {
		log.Fatal("查询字段信息失败:", err)
	}

	defaultVal := "NULL"
	if columnInfo.Default != nil {
		defaultVal = *columnInfo.Default
	}

	fmt.Printf("当前字段信息:\n")
	fmt.Printf("  类型: %s\n", columnInfo.Type)
	fmt.Printf("  默认值: %s\n", defaultVal)

	// 2. 修改字段默认值为 2
	fmt.Println("\n🔄 修改字段默认值为 2 (审核中)...")

	alterSQL := "ALTER TABLE hook_user MODIFY COLUMN is_active TINYINT DEFAULT 2 COMMENT '用户状态:0=禁用,1=正常,2=审核中'"

	err = db.Exec(alterSQL).Error
	if err != nil {
		log.Fatal("修改字段默认值失败:", err)
	}

	fmt.Println("✅ 字段默认值修改成功")

	// 3. 验证修改结果
	fmt.Println("\n✅ 验证修改结果...")

	err = db.Raw("SHOW COLUMNS FROM hook_user WHERE Field = 'is_active'").Scan(&columnInfo).Error
	if err != nil {
		log.Fatal("验证字段信息失败:", err)
	}

	newDefaultVal := "NULL"
	if columnInfo.Default != nil {
		newDefaultVal = *columnInfo.Default
	}

	fmt.Printf("修改后字段信息:\n")
	fmt.Printf("  类型: %s\n", columnInfo.Type)
	fmt.Printf("  默认值: %s\n", newDefaultVal)

	// 4. 确保管理员用户状态为正常
	fmt.Println("\n👑 确保管理员用户状态正确...")

	result := db.Exec("UPDATE hook_user SET is_active = 1 WHERE role IN ('admin', 'manager')")
	if result.Error != nil {
		log.Printf("⚠️  更新管理员状态失败: %v", result.Error)
	} else {
		fmt.Printf("✅ 确认了 %d 个管理员用户状态\n", result.RowsAffected)
	}

	// 5. 显示当前用户状态分布
	fmt.Println("\n📊 当前用户状态分布:")

	var stats []struct {
		IsActive int   `json:"is_active"`
		Count    int64 `json:"count"`
	}

	err = db.Raw("SELECT is_active, COUNT(*) as count FROM hook_user GROUP BY is_active ORDER BY is_active").Scan(&stats).Error
	if err != nil {
		log.Fatal("查询用户状态分布失败:", err)
	}

	for _, stat := range stats {
		statusName := getStatusName(stat.IsActive)
		fmt.Printf("  状态 %d (%s): %d 个用户\n", stat.IsActive, statusName, stat.Count)
	}

	fmt.Println("\n🎉 修复完成!")
	fmt.Println("\n📋 修复总结:")
	fmt.Println("  - is_active 字段默认值已改为 2 (审核中)")
	fmt.Println("  - 新注册用户将默认为审核状态")
	fmt.Println("  - 管理员用户状态已确认为正常")
	fmt.Println("  - 现有用户状态保持不变")
}

// getStatusName 获取状态名称
func getStatusName(status int) string {
	switch status {
	case 0:
		return "禁用"
	case 1:
		return "正常"
	case 2:
		return "审核中"
	default:
		return "未知"
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
