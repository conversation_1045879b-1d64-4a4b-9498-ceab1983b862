package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// User 用户模型
type User struct {
	ID       uint   `gorm:"primaryKey;autoIncrement"`
	Phone    string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	Role     string `gorm:"type:enum('admin','manager','user');default:'user'"`
	Nickname string `gorm:"type:varchar(100)"`
	Balance  int64  `gorm:"default:0"`
	IsActive int    `gorm:"type:tinyint;default:2"`
}

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

func main() {
	// 数据库连接配置
	// 注意：使用远程数据库配置，避免本地配置问题
	// 这些默认值指向远程服务器，确保与.env文件中的配置一致
	dbHost := getEnv("DB_HOST", "***********")          // 远程MySQL服务器
	dbPort := getEnv("DB_PORT", "3380")                 // 远程MySQL端口
	dbUser := getEnv("DB_USERNAME", "gmdns")            // 远程MySQL用户名
	dbPassword := getEnv("DB_PASSWORD", "Suyan15913..") // 远程MySQL密码
	dbName := getEnv("DB_DATABASE", "solve_web")        // 数据库名称

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)

	fmt.Printf("连接数据库: %s\n", dsn)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 获取底层sql.DB对象
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("获取数据库连接失败: %v", err)
	}
	defer sqlDB.Close()

	fmt.Println("数据库连接成功")

	// 查询所有管理员用户
	var users []User
	err = db.Where("role IN ?", []string{"admin", "manager"}).Find(&users).Error
	if err != nil {
		log.Fatalf("查询用户失败: %v", err)
	}

	fmt.Printf("\n找到 %d 个管理员用户:\n", len(users))
	fmt.Println("========================================")

	for _, user := range users {
		fmt.Printf("ID: %d\n", user.ID)
		fmt.Printf("手机号: %s\n", user.Phone)
		fmt.Printf("角色: %s\n", user.Role)
		fmt.Printf("昵称: %s\n", user.Nickname)
		fmt.Printf("状态: %v\n", user.IsActive)
		fmt.Printf("密码哈希: %s\n", user.Password)

		// 测试密码验证
		testPassword := "123456"
		err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(testPassword))
		if err != nil {
			fmt.Printf("❌ 密码验证失败: %v\n", err)
		} else {
			fmt.Printf("✅ 密码验证成功\n")
		}

		fmt.Println("----------------------------------------")
	}

	// 特别测试目标账号
	targetPhone := "13800000001"
	fmt.Printf("\n🔍 特别检查账号: %s\n", targetPhone)
	fmt.Println("========================================")

	var targetUser User
	err = db.Where("phone = ?", targetPhone).First(&targetUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			fmt.Printf("❌ 账号 %s 不存在\n", targetPhone)
		} else {
			fmt.Printf("❌ 查询失败: %v\n", err)
		}
	} else {
		fmt.Printf("✅ 找到账号: %s\n", targetUser.Phone)
		fmt.Printf("角色: %s\n", targetUser.Role)
		fmt.Printf("昵称: %s\n", targetUser.Nickname)
		fmt.Printf("状态: %v\n", targetUser.IsActive)
		fmt.Printf("密码哈希长度: %d\n", len(targetUser.Password))

		// 详细密码验证
		testPassword := "123456"
		fmt.Printf("\n测试密码: %s\n", testPassword)

		err = bcrypt.CompareHashAndPassword([]byte(targetUser.Password), []byte(testPassword))
		if err != nil {
			fmt.Printf("❌ 密码验证失败: %v\n", err)

			// 生成新的哈希进行对比
			newHash, _ := bcrypt.GenerateFromPassword([]byte(testPassword), bcrypt.DefaultCost)
			fmt.Printf("当前哈希: %s\n", targetUser.Password)
			fmt.Printf("新生成哈希: %s\n", string(newHash))
		} else {
			fmt.Printf("✅ 密码验证成功\n")
		}

		// 检查账户状态
		switch targetUser.IsActive {
		case 0:
			fmt.Printf("❌ 账户已禁用\n")
		case 1:
			fmt.Printf("✅ 账户已激活\n")
		case 2:
			fmt.Printf("⏳ 账户审核中\n")
		default:
			fmt.Printf("❓ 账户状态异常: %d\n", targetUser.IsActive)
		}
	}

	fmt.Println("\n🔧 修复建议:")
	if len(users) == 0 {
		fmt.Println("1. 数据库中没有管理员用户，需要创建")
	} else {
		fmt.Println("1. 检查密码哈希是否正确")
		fmt.Println("2. 确保账户状态为激活")
		fmt.Println("3. 验证数据库连接配置")
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
