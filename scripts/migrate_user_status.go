package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 获取数据库连接信息
	host := getEnv("DB_HOST", "***********")
	port := getEnv("DB_PORT", "3380")
	user := getEnv("DB_USER", "gmdns")
	password := getEnv("DB_PASSWORD", "Suyan15913..")
	dbname := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbname)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("🔄 开始用户状态字段迁移...")

	// 1. 检查当前字段类型
	fmt.Println("\n📋 检查当前字段类型...")
	var columnInfo struct {
		Field   string
		Type    string
		Null    string
		Key     string
		Default *string
		Extra   string
	}

	err = db.Raw("SHOW COLUMNS FROM hook_user WHERE Field = 'is_active'").Scan(&columnInfo).Error
	if err != nil {
		log.Fatal("查询字段信息失败:", err)
	}

	fmt.Printf("当前字段类型: %s\n", columnInfo.Type)

	// 如果已经是 tinyint 类型，则跳过迁移
	if columnInfo.Type == "tinyint(4)" || columnInfo.Type == "tinyint(1)" {
		fmt.Println("✅ 字段类型已经是 tinyint，无需迁移")

		// 检查数据分布
		checkDataDistribution(db)
		return
	}

	// 2. 创建备份表
	fmt.Println("\n💾 创建备份表...")
	err = db.Exec("CREATE TABLE IF NOT EXISTS hook_user_backup AS SELECT * FROM hook_user").Error
	if err != nil {
		log.Printf("⚠️  创建备份表失败（可能已存在）: %v", err)
	} else {
		fmt.Println("✅ 备份表创建成功")
	}

	// 3. 添加新的状态字段
	fmt.Println("\n🔧 添加新的状态字段...")
	err = db.Exec("ALTER TABLE hook_user ADD COLUMN is_active_new TINYINT DEFAULT 2 COMMENT '用户状态:0=禁用,1=正常,2=审核中'").Error
	if err != nil {
		log.Printf("⚠️  添加新字段失败（可能已存在）: %v", err)
	} else {
		fmt.Println("✅ 新字段添加成功")
	}

	// 4. 迁移现有数据
	fmt.Println("\n📊 迁移现有数据...")

	// 查询当前数据分布
	var stats []struct {
		IsActive interface{} `json:"is_active"`
		Count    int64       `json:"count"`
	}

	err = db.Raw("SELECT is_active, COUNT(*) as count FROM hook_user GROUP BY is_active").Scan(&stats).Error
	if err != nil {
		log.Fatal("查询数据分布失败:", err)
	}

	fmt.Println("原始数据分布:")
	for _, stat := range stats {
		fmt.Printf("  is_active = %v: %d 条记录\n", stat.IsActive, stat.Count)
	}

	// 执行数据迁移
	migrationSQL := `
		UPDATE hook_user SET is_active_new = CASE 
			WHEN is_active = 1 THEN 1  -- true -> 正常
			WHEN is_active = 0 THEN 0  -- false -> 禁用
			ELSE 2                     -- 默认 -> 审核中
		END
	`

	result := db.Exec(migrationSQL)
	if result.Error != nil {
		log.Fatal("数据迁移失败:", result.Error)
	}

	fmt.Printf("✅ 数据迁移完成，影响 %d 条记录\n", result.RowsAffected)

	// 5. 删除旧字段
	fmt.Println("\n🗑️  删除旧字段...")
	err = db.Exec("ALTER TABLE hook_user DROP COLUMN is_active").Error
	if err != nil {
		log.Fatal("删除旧字段失败:", err)
	}
	fmt.Println("✅ 旧字段删除成功")

	// 6. 重命名新字段
	fmt.Println("\n🔄 重命名新字段...")
	err = db.Exec("ALTER TABLE hook_user CHANGE COLUMN is_active_new is_active TINYINT DEFAULT 2 COMMENT '用户状态:0=禁用,1=正常,2=审核中'").Error
	if err != nil {
		log.Fatal("重命名字段失败:", err)
	}
	fmt.Println("✅ 字段重命名成功")

	// 7. 添加索引
	fmt.Println("\n📇 添加索引...")
	err = db.Exec("ALTER TABLE hook_user ADD INDEX idx_is_active_new (is_active)").Error
	if err != nil {
		log.Printf("⚠️  添加索引失败（可能已存在）: %v", err)
	} else {
		fmt.Println("✅ 索引添加成功")
	}

	// 8. 验证迁移结果
	fmt.Println("\n✅ 验证迁移结果...")
	checkDataDistribution(db)

	// 9. 更新管理员用户状态
	fmt.Println("\n👑 确保管理员用户状态正确...")
	result = db.Exec("UPDATE hook_user SET is_active = 1 WHERE role IN ('admin', 'manager')")
	if result.Error != nil {
		log.Printf("⚠️  更新管理员状态失败: %v", result.Error)
	} else {
		fmt.Printf("✅ 更新了 %d 个管理员用户状态\n", result.RowsAffected)
	}

	fmt.Println("\n🎉 用户状态字段迁移完成!")
	fmt.Println("\n📋 迁移总结:")
	fmt.Println("  - 字段类型: BOOLEAN → TINYINT")
	fmt.Println("  - 状态值: 0=禁用, 1=正常, 2=审核中")
	fmt.Println("  - 默认值: 2 (审核中)")
	fmt.Println("  - 管理员用户已设置为正常状态")
	fmt.Println("  - 备份表: hook_user_backup")
}

// checkDataDistribution 检查数据分布
func checkDataDistribution(db *gorm.DB) {
	var newStats []struct {
		IsActive int   `json:"is_active"`
		Count    int64 `json:"count"`
	}

	err := db.Raw("SELECT is_active, COUNT(*) as count FROM hook_user GROUP BY is_active ORDER BY is_active").Scan(&newStats).Error
	if err != nil {
		log.Printf("⚠️  查询新数据分布失败: %v", err)
		return
	}

	fmt.Println("当前数据分布:")
	for _, stat := range newStats {
		statusName := getStatusName(stat.IsActive)
		fmt.Printf("  状态 %d (%s): %d 条记录\n", stat.IsActive, statusName, stat.Count)
	}
}

// getStatusName 获取状态名称
func getStatusName(status int) string {
	switch status {
	case 0:
		return "禁用"
	case 1:
		return "正常"
	case 2:
		return "审核中"
	default:
		return "未知"
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
