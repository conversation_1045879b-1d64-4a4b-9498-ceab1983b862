#!/bin/bash

# 快速重启脚本 - 简单高效的Go服务重启工具

set -e

# 配置
SERVICE_NAME="solve-go-api"
BINARY_NAME="solve-api"
MAIN_FILE="main.go"
BUILD_DIR="build"
PORT=${PORT:-8080}

# 颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
info() { echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"; }
warn() { echo -e "${YELLOW}[$(date '+%H:%M:%S')]${NC} $1"; }
error() { echo -e "${RED}[$(date '+%H:%M:%S')]${NC} $1"; }

# 获取进程ID
get_pid() {
    lsof -ti:$PORT 2>/dev/null || pgrep -f "$BINARY_NAME" 2>/dev/null || echo ""
}

# 强制停止服务
force_stop() {
    local pids=$(get_pid)
    if [ -n "$pids" ]; then
        info "停止服务进程: $pids"
        echo "$pids" | xargs kill -9 2>/dev/null || true
        sleep 1
    fi
}

# 快速编译
quick_build() {
    info "快速编译..."
    mkdir -p $BUILD_DIR
    
    # 简单编译，优化速度
    go build -o $BUILD_DIR/$BINARY_NAME $MAIN_FILE
    
    if [ $? -eq 0 ]; then
        info "编译完成"
    else
        error "编译失败"
        exit 1
    fi
}

# 启动服务
start_service() {
    info "启动服务..."
    
    # 后台启动
    nohup ./$BUILD_DIR/$BINARY_NAME > /dev/null 2>&1 &
    local pid=$!
    
    # 等待启动
    sleep 2
    
    # 检查是否启动成功
    if kill -0 $pid 2>/dev/null; then
        info "服务启动成功 (PID: $pid, PORT: $PORT)"
        
        # 快速健康检查
        if command -v curl &> /dev/null; then
            sleep 1
            if curl -s "http://localhost:$PORT/health" > /dev/null 2>&1; then
                info "健康检查通过 ✓"
            else
                warn "健康检查失败，但服务已启动"
            fi
        fi
    else
        error "服务启动失败"
        exit 1
    fi
}

# 主要功能
main() {
    echo "🚀 快速重启 $SERVICE_NAME"
    echo "================================"
    
    # 1. 强制停止
    force_stop
    
    # 2. 快速编译
    quick_build
    
    # 3. 启动服务
    start_service
    
    echo "================================"
    info "重启完成! 🎉"
    
    # 显示状态
    local pid=$(get_pid)
    if [ -n "$pid" ]; then
        info "服务状态: 运行中 (PID: $pid)"
        info "访问地址: http://localhost:$PORT"
    fi
}

# 检查Go环境
if ! command -v go &> /dev/null; then
    error "Go环境未安装"
    exit 1
fi

if [ ! -f "$MAIN_FILE" ]; then
    error "找不到主文件: $MAIN_FILE"
    exit 1
fi

# 执行主函数
main
