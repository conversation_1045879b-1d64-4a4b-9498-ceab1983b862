package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 获取数据库连接信息
	host := getEnv("DB_HOST", "***********")
	port := getEnv("DB_PORT", "3380")
	user := getEnv("DB_USER", "gmdns")
	password := getEnv("DB_PASSWORD", "Suyan15913..")
	dbname := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbname)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("🔧 强制修复 is_active 字段默认值...")

	// 1. 检查当前字段信息
	fmt.Println("\n📋 步骤1: 检查当前字段信息...")

	var columnInfo struct {
		Field   string  `json:"field"`
		Type    string  `json:"type"`
		Null    string  `json:"null"`
		Key     string  `json:"key"`
		Default *string `json:"default"`
		Extra   string  `json:"extra"`
	}

	err = db.Raw("SHOW COLUMNS FROM hook_user WHERE Field = 'is_active'").Scan(&columnInfo).Error
	if err != nil {
		log.Fatal("查询字段信息失败:", err)
	}

	defaultVal := "NULL"
	if columnInfo.Default != nil {
		defaultVal = *columnInfo.Default
	}

	fmt.Printf("当前字段信息:\n")
	fmt.Printf("  类型: %s\n", columnInfo.Type)
	fmt.Printf("  默认值: %s\n", defaultVal)

	// 2. 强制修改字段定义
	fmt.Println("\n🔄 步骤2: 强制修改字段定义...")

	// 使用 ALTER TABLE 强制设置默认值
	alterSQL := "ALTER TABLE hook_user ALTER COLUMN is_active SET DEFAULT 2"

	err = db.Exec(alterSQL).Error
	if err != nil {
		log.Printf("⚠️  使用 ALTER COLUMN 失败: %v", err)

		// 如果 ALTER COLUMN 失败，尝试 MODIFY COLUMN
		fmt.Println("尝试使用 MODIFY COLUMN...")
		modifySQL := "ALTER TABLE hook_user MODIFY COLUMN is_active TINYINT NOT NULL DEFAULT 2 COMMENT '用户状态:0=禁用,1=正常,2=审核中'"

		err = db.Exec(modifySQL).Error
		if err != nil {
			log.Fatal("修改字段失败:", err)
		}
	}

	fmt.Println("✅ 字段默认值修改成功")

	// 3. 验证修改结果
	fmt.Println("\n✅ 步骤3: 验证修改结果...")

	err = db.Raw("SHOW COLUMNS FROM hook_user WHERE Field = 'is_active'").Scan(&columnInfo).Error
	if err != nil {
		log.Fatal("验证字段信息失败:", err)
	}

	newDefaultVal := "NULL"
	if columnInfo.Default != nil {
		newDefaultVal = *columnInfo.Default
	}

	fmt.Printf("修改后字段信息:\n")
	fmt.Printf("  类型: %s\n", columnInfo.Type)
	fmt.Printf("  默认值: %s\n", newDefaultVal)

	if newDefaultVal != "2" {
		fmt.Printf("❌ 警告: 默认值仍然不是 2，而是 %s\n", newDefaultVal)
	}

	// 4. 测试插入不指定 is_active 的记录
	fmt.Println("\n🧪 步骤4: 测试数据库默认值...")

	testPhone := "13777777777"

	// 删除可能存在的测试记录
	db.Exec("DELETE FROM hook_user WHERE phone = ?", testPhone)

	// 插入记录时不指定 is_active
	insertSQL := "INSERT INTO hook_user (phone, password, role, nickname, balance) VALUES (?, ?, ?, ?, ?)"

	err = db.Exec(insertSQL, testPhone, "test_password", "user", "测试默认值", 0).Error
	if err != nil {
		log.Fatal("插入测试记录失败:", err)
	}

	// 查询插入的记录
	var testUser struct {
		ID       uint   `json:"id"`
		Phone    string `json:"phone"`
		Nickname string `json:"nickname"`
		IsActive int    `json:"is_active"`
	}

	err = db.Raw("SELECT id, phone, nickname, is_active FROM hook_user WHERE phone = ?", testPhone).Scan(&testUser).Error
	if err != nil {
		log.Fatal("查询测试记录失败:", err)
	}

	fmt.Printf("测试记录信息:\n")
	fmt.Printf("  ID: %d\n", testUser.ID)
	fmt.Printf("  手机: %s\n", testUser.Phone)
	fmt.Printf("  昵称: %s\n", testUser.Nickname)
	fmt.Printf("  状态: %d (%s)\n", testUser.IsActive, getStatusName(testUser.IsActive))

	if testUser.IsActive == 2 {
		fmt.Println("✅ 数据库默认值正常工作!")
	} else {
		fmt.Printf("❌ 数据库默认值异常: 期望 2，实际 %d\n", testUser.IsActive)
	}

	// 5. 清理测试数据
	fmt.Println("\n🧹 步骤5: 清理测试数据...")

	err = db.Exec("DELETE FROM hook_user WHERE phone = ?", testPhone).Error
	if err != nil {
		log.Printf("⚠️  清理测试数据失败: %v", err)
	} else {
		fmt.Println("✅ 测试数据已清理")
	}

	// 6. 显示当前用户状态分布
	fmt.Println("\n📊 步骤6: 当前用户状态分布...")

	var stats []struct {
		IsActive int   `json:"is_active"`
		Count    int64 `json:"count"`
	}

	err = db.Raw("SELECT is_active, COUNT(*) as count FROM hook_user GROUP BY is_active ORDER BY is_active").Scan(&stats).Error
	if err != nil {
		log.Fatal("查询用户状态分布失败:", err)
	}

	for _, stat := range stats {
		statusName := getStatusName(stat.IsActive)
		fmt.Printf("  状态 %d (%s): %d 个用户\n", stat.IsActive, statusName, stat.Count)
	}

	fmt.Println("\n🎉 强制修复完成!")
	fmt.Println("\n📋 重要提醒:")
	fmt.Println("  - 请重启应用程序以确保更改生效")
	fmt.Println("  - GORM AutoMigrate 可能会重置默认值")
	fmt.Println("  - 建议在应用启动后再次检查字段默认值")
}

// getStatusName 获取状态名称
func getStatusName(status int) string {
	switch status {
	case 0:
		return "禁用"
	case 1:
		return "正常"
	case 2:
		return "审核中"
	default:
		return "未知"
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
