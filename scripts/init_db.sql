-- 创建数据库
CREATE DATABASE IF NOT EXISTS solve_web CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE solve_web;

-- 用户表
CREATE TABLE IF NOT EXISTS hook_user (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    role ENUM('admin','manager','user') DEFAULT 'user' COMMENT '角色',
    nickname VARCHAR(100) COMMENT '昵称',
    balance BIGINT DEFAULT 0 COMMENT '积分余额',
    is_active TINYINT DEFAULT 2 COMMENT '用户状态:0=禁用,1=正常,2=审核中',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后登录时间',
    INDEX idx_phone (phone),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 应用表
CREATE TABLE IF NOT EXISTS hook_apps (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '关联用户ID',
    name VARCHAR(100) NOT NULL COMMENT '应用名称',
    app_id VARCHAR(16) NOT NULL UNIQUE COMMENT '应用ID',
    app_secret VARCHAR(32) NOT NULL COMMENT '应用私钥',
    status INT DEFAULT 0 COMMENT '应用状态 0正常 1禁用',
    total_calls BIGINT UNSIGNED DEFAULT 0 COMMENT '累计调用次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_app_id (app_id),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES hook_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用表';

-- 积分变动记录表
CREATE TABLE IF NOT EXISTS hook_balance_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '关联用户ID',
    change_amount BIGINT NOT NULL COMMENT '变化金额',
    reason VARCHAR(255) COMMENT '变化原因',
    operator_id BIGINT UNSIGNED COMMENT '操作人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
    INDEX idx_user_id (user_id),
    INDEX idx_operator_id (operator_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES hook_user(id) ON DELETE CASCADE,
    FOREIGN KEY (operator_id) REFERENCES hook_user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分变动记录表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS hook_system_config (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键名',
    `value` TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_key (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 模型配置表
CREATE TABLE IF NOT EXISTS hook_models (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL UNIQUE COMMENT '模型名称',
    model_url VARCHAR(255) NOT NULL COMMENT '模型请求地址',
    model_key VARCHAR(255) NOT NULL COMMENT '模型调用凭证',
    model_type ENUM('OCR','solve') NOT NULL COMMENT '模型类型',
    role_system TEXT COMMENT 'system角色内容',
    role_user TEXT COMMENT 'user角色内容',
    temperature DECIMAL(3,2) DEFAULT 0.70 COMMENT '温度参数',
    top_p DECIMAL(3,2) DEFAULT 0.90 COMMENT 'TopP参数',
    top_k INT DEFAULT 50 COMMENT 'TopK参数',
    repetition_penalty DECIMAL(3,2) DEFAULT 1.00 COMMENT '重复惩罚',
    presence_penalty DECIMAL(3,2) DEFAULT 0.00 COMMENT '存在惩罚',
    response_format VARCHAR(50) DEFAULT 'json' COMMENT '返回格式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_model_name (model_name),
    INDEX idx_model_type (model_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型配置表';

-- 题库表
CREATE TABLE IF NOT EXISTS hook_question_bank (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    hash_key VARCHAR(32) NOT NULL COMMENT '哈希缓存键名',
    type INT NOT NULL COMMENT '问题类型 1判断题 2单选题 3多选题',
    content TEXT NOT NULL COMMENT '题干内容',
    content_clean TEXT NOT NULL COMMENT '清洗后的题干内容',
    options JSON COMMENT '问题选项',
    answer JSON COMMENT '问题答案',
    analysis TEXT COMMENT '问题解析',
    image_url VARCHAR(500) COMMENT '题干图片',
    user_url VARCHAR(500) COMMENT '用户图片',
    verified BOOLEAN DEFAULT FALSE COMMENT '验证状态',
    question_len INT NOT NULL COMMENT '字符串长度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_hash_key (hash_key),
    INDEX idx_type (type),
    INDEX idx_verified (verified),
    INDEX idx_question_len (question_len),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题库表';

-- 请求日志表
CREATE TABLE IF NOT EXISTS hook_solve_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    app_id VARCHAR(16) NOT NULL COMMENT '应用ID',
    user_url VARCHAR(500) NOT NULL COMMENT '用户请求图片',
    matched_id BIGINT UNSIGNED DEFAULT 0 COMMENT '命中的问题ID',
    ocr_token INT DEFAULT 0 COMMENT 'OCR模型消耗token',
    source VARCHAR(20) COMMENT '响应来源',
    data JSON COMMENT '响应内容',
    status INT NOT NULL COMMENT '响应状态 1成功 0失败',
    latency BIGINT NOT NULL COMMENT '响应耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
    INDEX idx_app_id (app_id),
    INDEX idx_matched_id (matched_id),
    INDEX idx_status (status),
    INDEX idx_source (source),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (matched_id) REFERENCES hook_question_bank(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='请求日志表';
