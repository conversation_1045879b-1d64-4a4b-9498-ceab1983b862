# Go服务管理脚本使用说明

## 📋 脚本概览

本目录包含了完整的Go服务管理脚本集合，支持编译、启动、停止、重启、监控等功能。

### 🔧 脚本列表

| 脚本名称 | 用途 | 复杂度 | 推荐场景 |
|---------|------|--------|----------|
| `quick_restart.sh` | 快速重启 | ⭐ | 开发环境快速重启 |
| `restart_service.sh` | 基础服务管理 | ⭐⭐ | 日常服务管理 |
| `service_manager.sh` | 高级服务管理 | ⭐⭐⭐ | 生产环境部署 |
| `monitor_service.sh` | 服务监控 | ⭐⭐ | 自动监控和恢复 |

## 🚀 快速开始

### 1. 快速重启（推荐用于开发）

```bash
# 最简单的重启方式
./scripts/quick_restart.sh
```

**功能**：
- 强制停止现有服务
- 快速编译
- 启动服务
- 健康检查

### 2. 基础服务管理

```bash
# 查看帮助
./scripts/restart_service.sh help

# 编译服务
./scripts/restart_service.sh build

# 启动服务
./scripts/restart_service.sh start

# 停止服务
./scripts/restart_service.sh stop

# 重启服务
./scripts/restart_service.sh restart

# 查看状态
./scripts/restart_service.sh status

# 查看日志
./scripts/restart_service.sh logs

# 强制停止
./scripts/restart_service.sh force-stop
```

### 3. 高级服务管理

```bash
# 编译生产版本
./scripts/service_manager.sh build --env=prod

# 安装为系统服务
sudo ./scripts/service_manager.sh install --enable

# 启动系统服务
sudo ./scripts/service_manager.sh start

# 查看服务状态
./scripts/service_manager.sh status

# Docker部署
./scripts/service_manager.sh docker-build
./scripts/service_manager.sh docker-run --port=8080

# 健康检查
./scripts/service_manager.sh health
```

### 4. 服务监控

```bash
# 开始监控（前台运行）
./scripts/monitor_service.sh start

# 后台监控
nohup ./scripts/monitor_service.sh start > /dev/null 2>&1 &

# 查看监控状态
./scripts/monitor_service.sh status

# 停止监控
./scripts/monitor_service.sh stop

# 执行一次检查
./scripts/monitor_service.sh check
```

## 📊 详细功能说明

### quick_restart.sh - 快速重启脚本

**特点**：
- 🚀 启动速度快
- 🎯 专注于重启功能
- 💡 适合开发环境

**使用场景**：
- 代码修改后快速重启
- 开发调试
- 简单的服务重启需求

### restart_service.sh - 基础服务管理脚本

**功能**：
- ✅ 完整的服务生命周期管理
- 📝 详细的日志记录
- 🔍 服务状态监控
- 🛠️ 编译和构建管理

**高级功能**：
- 进程管理和PID文件
- 健康检查
- 日志轮转
- 优雅停止和强制停止

### service_manager.sh - 高级服务管理脚本

**功能**：
- 🏗️ 高级编译选项
- 🐧 systemd服务集成
- 🐳 Docker支持
- 📊 性能监控
- 🔧 环境配置管理

**生产环境特性**：
- systemd服务安装
- 开机自启动
- 服务依赖管理
- 安全配置

### monitor_service.sh - 服务监控脚本

**功能**：
- 🔄 自动重启
- 📈 健康检查
- 📱 通知系统
- 📊 状态记录

**监控项目**：
- 进程存活检查
- 端口可访问性检查
- HTTP健康检查
- 自动故障恢复

## 🔧 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `PORT` | 8080 | 服务端口 |
| `GO_ENV` | dev | 运行环境 |
| `CHECK_INTERVAL` | 30 | 监控检查间隔（秒） |
| `MAX_FAILURES` | 3 | 最大失败次数 |

### 配置示例

```bash
# 设置端口
export PORT=9090

# 设置环境
export GO_ENV=prod

# 设置监控参数
export CHECK_INTERVAL=60
export MAX_FAILURES=5

# 运行脚本
./scripts/restart_service.sh restart
```

## 📁 文件结构

```
scripts/
├── quick_restart.sh          # 快速重启脚本
├── restart_service.sh        # 基础服务管理脚本
├── service_manager.sh        # 高级服务管理脚本
├── monitor_service.sh        # 服务监控脚本
├── reset_admin_password.sh   # 密码重置脚本
├── test_admin_login.sh       # 登录测试脚本
└── README_SERVICE_SCRIPTS.md # 本文档

build/                        # 编译输出目录
├── solve-api                 # 编译后的二进制文件

logs/                         # 日志目录
├── service.log               # 服务日志
├── monitor.log               # 监控日志
└── notifications.log         # 通知日志
```

## 🎯 使用场景推荐

### 开发环境
```bash
# 代码修改后快速重启
./scripts/quick_restart.sh

# 查看服务状态
./scripts/restart_service.sh status
```

### 测试环境
```bash
# 编译测试版本
./scripts/service_manager.sh build --env=test

# 启动服务
./scripts/restart_service.sh start

# 开始监控
./scripts/monitor_service.sh start &
```

### 生产环境
```bash
# 安装系统服务
sudo ./scripts/service_manager.sh install --enable

# 启动服务
sudo systemctl start solve-go-api

# 查看状态
sudo systemctl status solve-go-api

# 设置监控
nohup ./scripts/monitor_service.sh start > /dev/null 2>&1 &
```

## 🔍 故障排除

### 常见问题

1. **权限不足**
   ```bash
   chmod +x scripts/*.sh
   ```

2. **端口被占用**
   ```bash
   # 查找占用进程
   lsof -ti:8080
   
   # 强制停止
   ./scripts/restart_service.sh force-stop
   ```

3. **编译失败**
   ```bash
   # 检查Go环境
   go version
   
   # 清理依赖
   go mod tidy
   ```

4. **服务启动失败**
   ```bash
   # 查看日志
   ./scripts/restart_service.sh logs
   
   # 检查配置
   ./scripts/restart_service.sh status
   ```

### 日志查看

```bash
# 服务日志
tail -f logs/service.log

# 监控日志
tail -f logs/monitor.log

# 系统服务日志
sudo journalctl -u solve-go-api -f
```

## 📞 技术支持

如果遇到问题，请按以下步骤排查：

1. 检查Go环境和项目结构
2. 查看相关日志文件
3. 确认端口和权限配置
4. 验证网络连接和防火墙设置

## 🔄 更新和维护

定期更新脚本：
```bash
# 备份现有脚本
cp -r scripts scripts_backup_$(date +%Y%m%d)

# 更新脚本后重新设置权限
chmod +x scripts/*.sh
```

## 📈 性能优化建议

1. **开发环境**：使用 `quick_restart.sh` 提高重启速度
2. **生产环境**：使用 `service_manager.sh` 的systemd集成
3. **监控**：根据服务负载调整 `CHECK_INTERVAL`
4. **日志**：定期清理日志文件避免磁盘空间不足
