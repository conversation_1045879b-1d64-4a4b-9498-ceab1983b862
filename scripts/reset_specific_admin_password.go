package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// User 用户模型
type User struct {
	ID       uint   `gorm:"primaryKey;autoIncrement"`
	Phone    string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	Role     string `gorm:"type:enum('admin','manager','user');default:'user'"`
	Nickname string `gorm:"type:varchar(100)"`
	Balance  int64  `gorm:"default:0"`
	IsActive int    `gorm:"type:tinyint;default:2"`
}

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

func main() {
	// 数据库连接配置
	// 注意：使用远程数据库配置，避免本地配置问题
	dbHost := getEnv("DB_HOST", "***********")          // 远程MySQL服务器
	dbPort := getEnv("DB_PORT", "3380")                 // 远程MySQL端口
	dbUser := getEnv("DB_USERNAME", "gmdns")            // 远程MySQL用户名
	dbPassword := getEnv("DB_PASSWORD", "Suyan15913..") // 远程MySQL密码
	dbName := getEnv("DB_DATABASE", "solve_web")        // 数据库名称

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 获取底层sql.DB对象
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("获取数据库连接失败: %v", err)
	}
	defer sqlDB.Close()

	// 目标管理员账号
	targetPhone := "13800000001"
	newPassword := "123456"

	// 生成新密码的哈希值
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("生成密码哈希失败: %v", err)
	}

	fmt.Printf("目标账号: %s\n", targetPhone)
	fmt.Printf("新密码: %s\n", newPassword)
	fmt.Printf("新密码哈希值: %s\n", string(hashedPassword))
	fmt.Println()

	// 查找目标用户
	var user User
	err = db.Where("phone = ?", targetPhone).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			fmt.Printf("❌ 未找到手机号为 %s 的用户\n", targetPhone)

			// 询问是否创建新用户
			fmt.Println("是否创建新的管理员用户？")
			fmt.Println("创建新用户...")

			newUser := User{
				Phone:    targetPhone,
				Password: string(hashedPassword),
				Role:     "admin",
				Nickname: "超级管理员",
				Balance:  0,
				IsActive: 1, // 管理员默认激活
			}

			err = db.Create(&newUser).Error
			if err != nil {
				log.Fatalf("创建新用户失败: %v", err)
			}

			fmt.Printf("✅ 创建新管理员成功: %s (手机号: %s, 角色: %s)\n",
				newUser.Nickname, newUser.Phone, newUser.Role)
		} else {
			log.Fatalf("查询用户失败: %v", err)
		}
	} else {
		// 显示当前用户信息
		fmt.Printf("找到用户: %s (手机号: %s, 角色: %s, 状态: %v)\n",
			user.Nickname, user.Phone, user.Role, user.IsActive)

		// 重置密码
		err = db.Model(&user).Updates(map[string]interface{}{
			"password":  string(hashedPassword),
			"is_active": true, // 确保账户是激活状态
		}).Error

		if err != nil {
			log.Fatalf("重置密码失败: %v", err)
		}

		fmt.Printf("✅ 密码重置成功: %s (手机号: %s)\n", user.Nickname, user.Phone)

		// 如果不是管理员角色，提示升级
		if user.Role != "admin" {
			fmt.Printf("⚠️  当前用户角色为: %s，如需管理员权限，请手动修改角色\n", user.Role)

			// 可选：自动升级为管理员
			fmt.Println("自动升级为管理员角色...")
			err = db.Model(&user).Update("role", "admin").Error
			if err != nil {
				fmt.Printf("❌ 角色升级失败: %v\n", err)
			} else {
				fmt.Println("✅ 角色已升级为管理员")
			}
		}
	}

	// 验证密码是否正确设置
	fmt.Println("\n🔍 验证密码设置...")
	var verifyUser User
	err = db.Where("phone = ?", targetPhone).First(&verifyUser).Error
	if err != nil {
		log.Fatalf("验证查询失败: %v", err)
	}

	// 验证密码哈希
	err = bcrypt.CompareHashAndPassword([]byte(verifyUser.Password), []byte(newPassword))
	if err != nil {
		fmt.Printf("❌ 密码验证失败: %v\n", err)
	} else {
		fmt.Println("✅ 密码验证成功")
	}

	fmt.Println("\n🎉 管理员密码重置完成！")
	fmt.Printf("账号: %s\n", targetPhone)
	fmt.Printf("密码: %s\n", newPassword)
	fmt.Printf("角色: %s\n", verifyUser.Role)
	fmt.Printf("状态: %v\n", verifyUser.IsActive)

	fmt.Println("\n📋 登录测试命令:")
	fmt.Printf("curl -X POST http://localhost:8080/api/v1/login \\\n")
	fmt.Printf("  -H \"Content-Type: application/json\" \\\n")
	fmt.Printf("  -d '{\"phone\": \"%s\", \"password\": \"%s\"}'\n", targetPhone, newPassword)
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
