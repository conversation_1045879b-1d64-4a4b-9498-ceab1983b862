package main

import (
	"fmt"
	"log"

	"golang.org/x/crypto/bcrypt"
)

func main() {
	password := "123456"

	// 生成bcrypt哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("生成密码哈希失败: %v", err)
	}

	fmt.Printf("原始密码: %s\n", password)
	fmt.Printf("bcrypt哈希: %s\n", string(hashedPassword))

	// 验证哈希是否正确
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(password))
	if err != nil {
		log.Fatalf("密码验证失败: %v", err)
	}

	fmt.Println("✅ 密码哈希生成并验证成功！")

	// 显示SQL更新语句
	fmt.Println("\n📋 SQL更新语句:")
	fmt.Printf("UPDATE hook_user SET password = '%s' WHERE role IN ('admin', 'manager');\n", string(hashedPassword))
}
